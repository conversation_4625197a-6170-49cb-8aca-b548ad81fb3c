import { DocSection } from './types';

// Documentation structure configuration
export const documentationSections: DocSection[] = [
  {
    id: 'overview',
    title: 'Overview',
    icon: 'Home',
    subsections: [
      { id: 'introduction', title: 'Introduction' },
      { id: 'features', title: 'Key Features' },
      { id: 'target-audience', title: 'Target Audience' }
    ]
  },
  {
    id: 'getting-started',
    title: 'Getting Started',
    icon: 'Play',
    subsections: [
      { id: 'prerequisites', title: 'Prerequisites' },
      { id: 'installation', title: 'Installation' },
      { id: 'configuration', title: 'Configuration' },
      { id: 'development-server', title: 'Development Server' }
    ]
  },
  {
    id: 'architecture',
    title: 'Architecture & Tech Stack',
    icon: 'Layers',
    subsections: [
      { id: 'technology-overview', title: 'Technology Overview' },
      { id: 'project-structure', title: 'Project Structure' },
      { id: 'design-system', title: 'Design System' },
      { id: 'state-management', title: 'State Management' }
    ]
  },
  {
    id: 'features',
    title: 'Admin Panel Features',
    icon: 'Settings',
    subsections: [
      { id: 'dashboard', title: 'Dashboard' },
      { id: 'content-management', title: 'Content Management' },
      { id: 'user-settings', title: 'Settings' },
      { id: 'development-tools', title: 'Development Tools' }
    ]
  },
  {
    id: 'components',
    title: 'Component Documentation',
    icon: 'Package',
    subsections: [
      { id: 'ui-components', title: 'UI Components' },
      { id: 'admin-components', title: 'Admin Components' },
      { id: 'custom-hooks', title: 'Custom Hooks' },
      { id: 'animations', title: 'Animation Components' }
    ]
  },
  {
    id: 'data-management',
    title: 'API & Data Management',
    icon: 'Database',
    subsections: [
      { id: 'data-models', title: 'Data Models' },
      { id: 'form-handling', title: 'Form Handling' },
      { id: 'state-patterns', title: 'State Patterns' },
      { id: 'mock-data', title: 'Mock Data Structure' }
    ]
  },
  {
    id: 'styling',
    title: 'Styling & Theming',
    icon: 'Palette',
    subsections: [
      { id: 'tailwind-config', title: 'Tailwind Configuration' },
      { id: 'design-tokens', title: 'Design Tokens' },
      { id: 'component-styling', title: 'Component Styling' },
      { id: 'responsive-design', title: 'Responsive Design' }
    ]
  },
  {
    id: 'development',
    title: 'Development Guidelines',
    icon: 'Code',
    subsections: [
      { id: 'code-organization', title: 'Code Organization' },
      { id: 'typescript-practices', title: 'TypeScript Best Practices' },
      { id: 'component-guidelines', title: 'Component Guidelines' },
      { id: 'testing', title: 'Testing' }
    ]
  },
  {
    id: 'deployment',
    title: 'Deployment & Production',
    icon: 'Rocket',
    subsections: [
      { id: 'build-process', title: 'Build Process' },
      { id: 'environment-config', title: 'Environment Configuration' },
      { id: 'deployment-options', title: 'Deployment Options' },
      { id: 'performance', title: 'Performance Optimization' }
    ]
  },
  {
    id: 'troubleshooting',
    title: 'Troubleshooting & FAQ',
    icon: 'HelpCircle',
    subsections: [
      { id: 'common-issues', title: 'Common Issues' },
      { id: 'development-tips', title: 'Development Tips' },
      { id: 'performance-tips', title: 'Performance Tips' },
      { id: 'faq', title: 'Frequently Asked Questions' }
    ]
  }
];
