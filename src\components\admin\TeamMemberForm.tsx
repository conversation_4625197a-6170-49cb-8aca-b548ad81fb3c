import { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Upload, X } from "lucide-react";
import { TeamMember, CreateTeamMember, UpdateTeamMember } from "@/types/team";

interface TeamMemberFormProps {
  member?: TeamMember;
  onSubmit: (data: CreateTeamMember | UpdateTeamMember) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function TeamMemberForm({ member, onSubmit, onCancel, isLoading = false }: TeamMemberFormProps) {
  const [formData, setFormData] = useState({
    full_name: member?.full_name || "",
    position: member?.position || "",
    bio: member?.bio || "",
    email: member?.email || "",
    linkedin_url: member?.linkedin_url || "",
    github_url: member?.github_url || "",
    is_active: member?.is_active ?? true,
    display_order: member?.display_order || 0,
    profile_image: member?.profile_image || ""
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [previewImage, setPreviewImage] = useState<string | null>(member?.profile_image || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.full_name.trim()) {
      newErrors.full_name = "Full name is required";
    } else if (formData.full_name.length > 120) {
      newErrors.full_name = "Full name must be 120 characters or less";
    }

    if (!formData.position.trim()) {
      newErrors.position = "Position is required";
    } else if (formData.position.length > 100) {
      newErrors.position = "Position must be 100 characters or less";
    }

    // Optional field validations
    if (formData.email && formData.email.length > 190) {
      newErrors.email = "Email must be 190 characters or less";
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (formData.linkedin_url && formData.linkedin_url.length > 300) {
      newErrors.linkedin_url = "LinkedIn URL must be 300 characters or less";
    }

    if (formData.github_url && formData.github_url.length > 300) {
      newErrors.github_url = "GitHub URL must be 300 characters or less";
    }

    if (formData.profile_image && formData.profile_image.length > 500) {
      newErrors.profile_image = "Profile image URL must be 500 characters or less";
    }

    // URL validations
    if (formData.linkedin_url && !/^https?:\/\/.+/.test(formData.linkedin_url)) {
      newErrors.linkedin_url = "Please enter a valid URL starting with http:// or https://";
    }

    if (formData.github_url && !/^https?:\/\/.+/.test(formData.github_url)) {
      newErrors.github_url = "Please enter a valid URL starting with http:// or https://";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const submitData = {
      ...formData,
      bio: formData.bio.trim() || null,
      email: formData.email.trim() || null,
      linkedin_url: formData.linkedin_url.trim() || null,
      github_url: formData.github_url.trim() || null,
      profile_image: formData.profile_image.trim() || null,
    };

    if (member) {
      onSubmit({ id: member.id, ...submitData });
    } else {
      onSubmit(submitData);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setErrors(prev => ({ ...prev, profile_image: "Please select an image file" }));
        return;
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, profile_image: "Image must be smaller than 5MB" }));
        return;
      }

      // Create preview
      const reader = new FileReader();
      reader.onload = (event) => {
        const result = event.target?.result as string;
        setPreviewImage(result);
        setFormData(prev => ({ ...prev, profile_image: result }));
        setErrors(prev => ({ ...prev, profile_image: "" }));
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setPreviewImage(null);
    setFormData(prev => ({ ...prev, profile_image: "" }));
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Profile Image Upload */}
      <div className="space-y-2">
        <Label>Profile Image</Label>
        <div className="flex items-center gap-4">
          <Avatar className="w-20 h-20">
            <AvatarImage src={previewImage || "/api/placeholder/64/64"} alt="Profile preview" />
            <AvatarFallback>
              {formData.full_name.split(' ').map(n => n[0]).join('').toUpperCase() || 'TM'}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading}
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload Image
            </Button>
            {previewImage && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={removeImage}
                disabled={isLoading}
              >
                <X className="w-4 h-4 mr-2" />
                Remove
              </Button>
            )}
          </div>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />
        {errors.profile_image && (
          <p className="text-sm text-destructive">{errors.profile_image}</p>
        )}
      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="full_name">Full Name *</Label>
          <Input
            id="full_name"
            value={formData.full_name}
            onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
            placeholder="Enter full name"
            disabled={isLoading}
            className={errors.full_name ? "border-destructive" : ""}
          />
          {errors.full_name && (
            <p className="text-sm text-destructive">{errors.full_name}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="position">Position *</Label>
          <Input
            id="position"
            value={formData.position}
            onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value }))}
            placeholder="Enter position/role"
            disabled={isLoading}
            className={errors.position ? "border-destructive" : ""}
          />
          {errors.position && (
            <p className="text-sm text-destructive">{errors.position}</p>
          )}
        </div>
      </div>

      {/* Contact Information */}
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
          placeholder="Enter email address"
          disabled={isLoading}
          className={errors.email ? "border-destructive" : ""}
        />
        {errors.email && (
          <p className="text-sm text-destructive">{errors.email}</p>
        )}
      </div>

      {/* Bio */}
      <div className="space-y-2">
        <Label htmlFor="bio">Bio</Label>
        <Textarea
          id="bio"
          value={formData.bio}
          onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
          placeholder="Enter bio/description"
          rows={4}
          disabled={isLoading}
        />
      </div>

      {/* Social Links */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="linkedin_url">LinkedIn URL</Label>
          <Input
            id="linkedin_url"
            value={formData.linkedin_url}
            onChange={(e) => setFormData(prev => ({ ...prev, linkedin_url: e.target.value }))}
            placeholder="https://linkedin.com/in/username"
            disabled={isLoading}
            className={errors.linkedin_url ? "border-destructive" : ""}
          />
          {errors.linkedin_url && (
            <p className="text-sm text-destructive">{errors.linkedin_url}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="github_url">GitHub URL</Label>
          <Input
            id="github_url"
            value={formData.github_url}
            onChange={(e) => setFormData(prev => ({ ...prev, github_url: e.target.value }))}
            placeholder="https://github.com/username"
            disabled={isLoading}
            className={errors.github_url ? "border-destructive" : ""}
          />
          {errors.github_url && (
            <p className="text-sm text-destructive">{errors.github_url}</p>
          )}
        </div>
      </div>

      {/* Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="display_order">Display Order</Label>
          <Input
            id="display_order"
            type="number"
            value={formData.display_order}
            onChange={(e) => setFormData(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
            placeholder="0"
            disabled={isLoading}
            min="0"
          />
        </div>

        <div className="flex items-center space-x-2 pt-6">
          <Switch
            id="is_active"
            checked={formData.is_active}
            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
            disabled={isLoading}
          />
          <Label htmlFor="is_active">Active Member</Label>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : member ? "Update Member" : "Add Member"}
        </Button>
      </div>
    </form>
  );
}
