// TypeScript interface matching the services database table schema

export type ServiceCategory = 
  | 'web_development'
  | 'mobile_development'
  | 'ui_ux_design'
  | 'consulting'
  | 'maintenance'
  | 'custom_software'
  | 'other';

export const SERVICE_CATEGORY_CHOICES: Record<ServiceCategory, string> = {
  web_development: 'Web Development',
  mobile_development: 'Mobile Development',
  ui_ux_design: 'UI/UX Design',
  consulting: 'Consulting',
  maintenance: 'Maintenance & Support',
  custom_software: 'Custom Software',
  other: 'Other',
};

export interface Service {
  id: number;
  title: string;
  category: ServiceCategory;
  short_description: string | null;
  full_description: string | null;
  icon: string | null;
  image: string | null;
  features: string[] | null;
  price_range: string | null;
  duration: string | null;
  is_featured: boolean;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

// Type for creating a new service (without auto-generated fields)
export interface CreateService {
  title: string;
  category?: ServiceCategory;
  short_description?: string | null;
  full_description?: string | null;
  icon?: string | null;
  image?: string | null;
  features?: string[] | null;
  price_range?: string | null;
  duration?: string | null;
  is_featured?: boolean;
  is_active?: boolean;
  display_order?: number;
}

// Type for updating a service (all fields optional except id)
export interface UpdateService {
  id: number;
  title?: string;
  category?: ServiceCategory;
  short_description?: string | null;
  full_description?: string | null;
  icon?: string | null;
  image?: string | null;
  features?: string[] | null;
  price_range?: string | null;
  duration?: string | null;
  is_featured?: boolean;
  is_active?: boolean;
  display_order?: number;
}

// Helper function to get category display name
export const getCategoryDisplayName = (category: ServiceCategory): string => {
  return SERVICE_CATEGORY_CHOICES[category] || 'Other';
};

// Helper function to get all category options for dropdowns
export const getCategoryOptions = (): Array<{ value: ServiceCategory; label: string }> => {
  return Object.entries(SERVICE_CATEGORY_CHOICES).map(([value, label]) => ({
    value: value as ServiceCategory,
    label,
  }));
};
