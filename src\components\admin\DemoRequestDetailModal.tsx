import { 
  <PERSON>alog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  Mail, 
  Phone, 
  Building, 
  User, 
  Calendar, 
  Clock, 
  DollarSign, 
  FileText, 
  MessageSquare,
  MapPin,
  Globe
} from "lucide-react";

interface DemoRequest {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  company_name: string;
  job_title?: string;
  company_size?: string;
  industry?: string;
  project_type: string;
  budget_range?: string;
  timeline?: string;
  project_description: string;
  specific_requirements?: string;
  preferred_demo_date?: string;
  preferred_demo_time?: string;
  how_did_you_hear?: string;
  status: string;
  assigned_to?: string;
  demo_scheduled_at?: string;
  notes?: string;
  created_at: string;
}

interface DemoRequestDetailModalProps {
  request: DemoRequest | null;
  isOpen: boolean;
  onClose: () => void;
}

const getStatusBadge = (status: string) => {
  const variants = {
    new: "secondary",
    pending: "secondary", 
    scheduled: "default",
    "in-progress": "outline",
    completed: "default",
    cancelled: "destructive"
  } as const;

  return (
    <Badge variant={variants[status as keyof typeof variants] || "secondary"}>
      {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
    </Badge>
  );
};

export function DemoRequestDetailModal({ request, isOpen, onClose }: DemoRequestDetailModalProps) {
  if (!request) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Demo Request #{request.id}
            {getStatusBadge(request.status)}
          </DialogTitle>
          <DialogDescription>
            Submitted on {new Date(request.created_at).toLocaleDateString()} at {new Date(request.created_at).toLocaleTimeString()}
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <User className="w-5 h-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Full Name</label>
                <p className="text-sm">{request.first_name} {request.last_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Email</label>
                <p className="text-sm flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  {request.email}
                </p>
              </div>
              {request.phone && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Phone</label>
                  <p className="text-sm flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    {request.phone}
                  </p>
                </div>
              )}
              {request.job_title && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Job Title</label>
                  <p className="text-sm">{request.job_title}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Company Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Building className="w-5 h-5" />
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Company Name</label>
                <p className="text-sm">{request.company_name}</p>
              </div>
              {request.industry && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Industry</label>
                  <p className="text-sm">{request.industry}</p>
                </div>
              )}
              {request.company_size && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Company Size</label>
                  <p className="text-sm">{request.company_size} employees</p>
                </div>
              )}
              {request.how_did_you_hear && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">How did you hear about us?</label>
                  <p className="text-sm flex items-center gap-2">
                    <Globe className="w-4 h-4" />
                    {request.how_did_you_hear}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Project Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Project Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Project Type</label>
              <p className="text-sm font-medium">{request.project_type}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Project Description</label>
              <p className="text-sm">{request.project_description}</p>
            </div>
            {request.specific_requirements && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Specific Requirements</label>
                <p className="text-sm">{request.specific_requirements}</p>
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {request.budget_range && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Budget Range</label>
                  <p className="text-sm flex items-center gap-2">
                    <DollarSign className="w-4 h-4" />
                    {request.budget_range}
                  </p>
                </div>
              )}
              {request.timeline && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Timeline</label>
                  <p className="text-sm flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    {request.timeline}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Demo Scheduling */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Demo Scheduling
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {request.preferred_demo_date && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Preferred Demo Date</label>
                  <p className="text-sm">{new Date(request.preferred_demo_date).toLocaleDateString()}</p>
                </div>
              )}
              {request.preferred_demo_time && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Preferred Demo Time</label>
                  <p className="text-sm">{request.preferred_demo_time}</p>
                </div>
              )}
            </div>
            {request.demo_scheduled_at && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Scheduled Demo</label>
                <p className="text-sm">
                  {new Date(request.demo_scheduled_at).toLocaleDateString()} at {new Date(request.demo_scheduled_at).toLocaleTimeString()}
                </p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-muted-foreground">Assigned To</label>
              <p className="text-sm">{request.assigned_to || "Unassigned"}</p>
            </div>
          </CardContent>
        </Card>

        {/* Internal Notes */}
        {request.notes && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <MessageSquare className="w-5 h-5" />
                Internal Notes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">{request.notes}</p>
            </CardContent>
          </Card>
        )}
      </DialogContent>
    </Dialog>
  );
}
