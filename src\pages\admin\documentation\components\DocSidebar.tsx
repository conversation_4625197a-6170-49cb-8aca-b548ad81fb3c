import { useState } from 'react';
import { 
  Home, 
  Play, 
  Layers, 
  Settings, 
  Package, 
  Database, 
  Palette, 
  Code, 
  Rocket, 
  HelpCircle,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { documentationSections } from '../config';
import { DocNavigationItem } from '../types';

// Icon mapping
const iconMap = {
  Home,
  Play,
  Layers,
  Settings,
  Package,
  Database,
  Palette,
  Code,
  Rocket,
  HelpCircle
};

interface DocSidebarProps {
  activeSection: string;
  activeSubsection?: string;
  onNavigate: (item: DocNavigationItem) => void;
  className?: string;
}

export function DocSidebar({ 
  activeSection, 
  activeSubsection, 
  onNavigate, 
  className 
}: DocSidebarProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>([activeSection]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const handleSectionClick = (sectionId: string) => {
    toggleSection(sectionId);
    onNavigate({ section: sectionId });
  };

  const handleSubsectionClick = (sectionId: string, subsectionId: string) => {
    onNavigate({ section: sectionId, subsection: subsectionId });
  };

  return (
    <div className={cn("w-80 bg-card border-r border-border h-full overflow-y-auto", className)}>
      <div className="p-6 border-b border-border">
        <h2 className="text-lg font-semibold text-foreground">Documentation</h2>
        <p className="text-sm text-muted-foreground mt-1">
          Complete guide to the admin panel
        </p>
      </div>

      <nav className="p-4">
        <div className="space-y-2">
          {documentationSections.map((section) => {
            const IconComponent = section.icon ? iconMap[section.icon as keyof typeof iconMap] : null;
            const isExpanded = expandedSections.includes(section.id);
            const isActive = activeSection === section.id;
            const hasSubsections = section.subsections && section.subsections.length > 0;

            return (
              <div key={section.id} className="space-y-1">
                <button
                  onClick={() => handleSectionClick(section.id)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors",
                    "hover:bg-accent/50",
                    isActive && "bg-primary/10 text-primary font-medium"
                  )}
                >
                  {IconComponent && <IconComponent className="w-4 h-4 flex-shrink-0" />}
                  <span className="flex-1 text-sm">{section.title}</span>
                  {hasSubsections && (
                    isExpanded ? (
                      <ChevronDown className="w-4 h-4 flex-shrink-0" />
                    ) : (
                      <ChevronRight className="w-4 h-4 flex-shrink-0" />
                    )
                  )}
                </button>

                {hasSubsections && isExpanded && (
                  <div className="ml-7 space-y-1">
                    {section.subsections!.map((subsection) => {
                      const isSubsectionActive = 
                        activeSection === section.id && activeSubsection === subsection.id;

                      return (
                        <button
                          key={subsection.id}
                          onClick={() => handleSubsectionClick(section.id, subsection.id)}
                          className={cn(
                            "w-full text-left px-3 py-1.5 text-sm rounded-md transition-colors",
                            "hover:bg-accent/30",
                            isSubsectionActive && "bg-primary/10 text-primary font-medium"
                          )}
                        >
                          {subsection.title}
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </nav>
    </div>
  );
}
