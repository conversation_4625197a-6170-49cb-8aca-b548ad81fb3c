// TypeScript interface matching the team_members database table schema
export interface TeamMember {
  id: number;
  full_name: string;
  position: string;
  bio: string | null;
  profile_image: string | null;
  email: string | null;
  linkedin_url: string | null;
  github_url: string | null;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

// Type for creating a new team member (without auto-generated fields)
export interface CreateTeamMember {
  full_name: string;
  position: string;
  bio?: string | null;
  profile_image?: string | null;
  email?: string | null;
  linkedin_url?: string | null;
  github_url?: string | null;
  is_active?: boolean;
  display_order?: number;
}

// Type for updating a team member (all fields optional except id)
export interface UpdateTeamMember {
  id: number;
  full_name?: string;
  position?: string;
  bio?: string | null;
  profile_image?: string | null;
  email?: string | null;
  linkedin_url?: string | null;
  github_url?: string | null;
  is_active?: boolean;
  display_order?: number;
}
