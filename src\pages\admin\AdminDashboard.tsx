import { TrendingUp, Users, MessageSquare, FileText, Eye, Clock, CheckCircle, AlertCircle } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Mock data for demonstration
const metrics = [
  {
    title: "Total Demo Requests",
    value: "156",
    change: "+12%",
    trend: "up",
    icon: MessageSquare,
    description: "From last month"
  },
  {
    title: "Pending Demos",
    value: "8",
    change: "+2",
    trend: "up",
    icon: Clock,
    description: "Awaiting scheduling"
  },
  {
    title: "Active Team Members",
    value: "24",
    change: "+1",
    trend: "up",
    icon: Users,
    description: "Recently added"
  },
  {
    title: "Published Resources",
    value: "42",
    change: "1.2k",
    trend: "up",
    icon: FileText,
    description: "Total views"
  }
];

const recentDemos = [
  {
    id: 1,
    name: "<PERSON>",
    company: "TechCorp Inc.",
    email: "<EMAIL>",
    project: "E-commerce Platform",
    status: "scheduled",
    date: "2024-01-25",
    assignedTo: "John Smith"
  },
  {
    id: 2,
    name: "Michael Chen",
    company: "StartupXYZ",
    email: "<EMAIL>",
    project: "Mobile App",
    status: "pending",
    date: "2024-01-23",
    assignedTo: "Unassigned"
  },
  {
    id: 3,
    name: "Emma Wilson",
    company: "DataFlow Solutions",
    email: "<EMAIL>",
    project: "Analytics Dashboard",
    status: "completed",
    date: "2024-01-22",
    assignedTo: "Alice Brown"
  },
  {
    id: 4,
    name: "Robert Davis",
    company: "CloudTech Ltd.",
    email: "<EMAIL>",
    project: "Cloud Migration",
    status: "in-progress",
    date: "2024-01-20",
    assignedTo: "John Smith"
  }
];

const getStatusBadge = (status: string) => {
  const statusConfig = {
    scheduled: { variant: "default" as const, icon: CheckCircle },
    pending: { variant: "secondary" as const, icon: Clock },
    completed: { variant: "default" as const, icon: CheckCircle },
    "in-progress": { variant: "outline" as const, icon: AlertCircle }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="w-3 h-3" />
      {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
    </Badge>
  );
};

export default function AdminDashboard() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard Overview</h1>
          <p className="text-muted-foreground mt-1">
            Welcome back! Here's what's happening with your business today.
          </p>
        </div>
        <Button variant="outline">
          <Eye className="w-4 h-4 mr-2" />
          View Website
        </Button>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          return (
            <Card key={metric.title} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {metric.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">{metric.value}</div>
                <div className="flex items-center gap-1 text-xs text-success hidden">
                  <TrendingUp className="h-3 w-3" />
                  <span>{metric.change}</span>
                  <span className="text-muted-foreground">{metric.description}</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Demo Requests */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Demo Requests</CardTitle>
                  <CardDescription>Latest requests from potential clients</CardDescription>
                </div>
                <Button variant="outline" size="sm">View All</Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Contact</TableHead>
                    <TableHead>Project</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Assigned</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentDemos.map((demo) => (
                    <TableRow key={demo.id} className="hover:bg-muted/50">
                      <TableCell>
                        <div>
                          <div className="font-medium">{demo.name}</div>
                          <div className="text-sm text-muted-foreground">{demo.company}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{demo.project}</div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(demo.status)}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {new Date(demo.date).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-sm">
                        {demo.assignedTo}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common management tasks</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <Users className="w-4 h-4 mr-2" />
                Add Team Member
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <FileText className="w-4 h-4 mr-2" />
                Create Resource
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <MessageSquare className="w-4 h-4 mr-2" />
                Update Services
              </Button>
              <Button className="w-full justify-start" variant="outline" disabled>
                <TrendingUp className="w-4 h-4 mr-2" />
                View Analytics
              </Button>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card className="mt-6 hidden">
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Website Status</span>
                <Badge variant="default">Online</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Database</span>
                <Badge variant="default">Healthy</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Last Backup</span>
                <span className="text-sm text-muted-foreground">2 hours ago</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}