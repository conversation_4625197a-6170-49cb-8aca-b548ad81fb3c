import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Save, Eye, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export default function About() {
  const { toast } = useToast();
  const [isPreview, setIsPreview] = useState(false);
  const [aboutData, setAboutData] = useState({
    title: "About TechFlow Solutions",
    description: "We are a dynamic software development company specializing in cutting-edge web applications, mobile solutions, and cloud infrastructure. Our team of experienced developers and designers work collaboratively to bring your digital vision to life.",
    main_image: "",
    secondary_image: "",
    company_name: "TechFlow Solutions",
    established_year: 2018,
    mission_statement: "To deliver innovative software solutions that transform businesses and drive digital success.",
    vision_statement: "To be the leading technology partner for companies seeking digital transformation.",
    values: "Innovation, Quality, Client Focus, Team Collaboration, Continuous Learning",
    is_active: true,
    display_order: 0
  });

  const handleSave = () => {
    toast({
      title: "About Us Updated",
      description: "Company information has been successfully saved.",
    });
  };

  if (isPreview) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">About Us Preview</h1>
          <Button onClick={() => setIsPreview(false)} variant="outline">
            <X className="w-4 h-4 mr-2" />
            Close Preview
          </Button>
        </div>

        <Card>
          <CardContent className="p-8">
            <div className="max-w-4xl mx-auto space-y-8">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-foreground mb-4">{aboutData.title}</h1>
                <h2 className="text-2xl font-semibold text-foreground mb-2">{aboutData.company_name}</h2>
                <p className="text-xl text-muted-foreground">Established {aboutData.established_year}</p>
              </div>

              {aboutData.main_image && (
                <div className="text-center">
                  <img
                    src={aboutData.main_image}
                    alt="Main company image"
                    className="max-w-full h-auto rounded-lg mx-auto"
                  />
                </div>
              )}

              <div className="prose max-w-none">
                <p className="text-lg leading-relaxed">{aboutData.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Our Mission</h3>
                  <p className="text-muted-foreground">{aboutData.mission_statement}</p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3">Our Vision</h3>
                  <p className="text-muted-foreground">{aboutData.vision_statement}</p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3">Our Values</h3>
                  <p className="text-muted-foreground">{aboutData.values}</p>
                </div>
              </div>

              {aboutData.secondary_image && (
                <div className="text-center pt-8 border-t">
                  <img
                    src={aboutData.secondary_image}
                    alt="Secondary company image"
                    className="max-w-full h-auto rounded-lg mx-auto"
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">About Us Management</h1>
        <div className="flex gap-2">
          <Button onClick={() => setIsPreview(true)} variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button onClick={handleSave}>
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={aboutData.title}
                onChange={(e) => setAboutData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="About Us page title"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="company_name">Company Name</Label>
                <Input
                  id="company_name"
                  value={aboutData.company_name}
                  onChange={(e) => setAboutData(prev => ({ ...prev, company_name: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="established_year">Established Year</Label>
                <Input
                  id="established_year"
                  type="number"
                  min="1900"
                  max="2100"
                  value={aboutData.established_year}
                  onChange={(e) => setAboutData(prev => ({ ...prev, established_year: parseInt(e.target.value) || 0 }))}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                rows={4}
                value={aboutData.description}
                onChange={(e) => setAboutData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="mission_statement">Mission Statement</Label>
              <Textarea
                id="mission_statement"
                rows={3}
                value={aboutData.mission_statement}
                onChange={(e) => setAboutData(prev => ({ ...prev, mission_statement: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="vision_statement">Vision Statement</Label>
              <Textarea
                id="vision_statement"
                rows={3}
                value={aboutData.vision_statement}
                onChange={(e) => setAboutData(prev => ({ ...prev, vision_statement: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="values">Values</Label>
              <Textarea
                id="values"
                rows={3}
                value={aboutData.values}
                onChange={(e) => setAboutData(prev => ({ ...prev, values: e.target.value }))}
                placeholder="Enter company values (comma-separated or as text)"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Images & Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="main_image">Main Image URL</Label>
              <Input
                id="main_image"
                type="url"
                value={aboutData.main_image}
                onChange={(e) => setAboutData(prev => ({ ...prev, main_image: e.target.value }))}
                placeholder="https://example.com/main-image.jpg"
              />
            </div>

            <div>
              <Label htmlFor="secondary_image">Secondary Image URL</Label>
              <Input
                id="secondary_image"
                type="url"
                value={aboutData.secondary_image}
                onChange={(e) => setAboutData(prev => ({ ...prev, secondary_image: e.target.value }))}
                placeholder="https://example.com/secondary-image.jpg"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                checked={aboutData.is_active}
                onCheckedChange={(checked) => setAboutData(prev => ({ ...prev, is_active: checked }))}
              />
              <Label htmlFor="is_active">Active</Label>
            </div>

            <div>
              <Label htmlFor="display_order">Display Order</Label>
              <Input
                id="display_order"
                type="number"
                value={aboutData.display_order}
                onChange={(e) => setAboutData(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
                placeholder="0"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}