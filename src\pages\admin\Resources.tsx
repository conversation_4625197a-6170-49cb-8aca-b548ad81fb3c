import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Plus, Edit, Trash2, Eye, Upload, FileText, Video, Download, ExternalLink, Clock, Users } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Resource {
  id: number;
  title: string;
  description: string;
  author: string;
  type: string;
  difficultyLevel: string;
  readingTime: number;
  contentUrl: string;
  isExternal: boolean;
  featuredImage: string;
  tags: string[];
  publishedDate: string;
  views: number;
  downloads: number;
  featured: boolean;
  active: boolean;
}

export default function Resources() {
  const { toast } = useToast();
  const [isAddingResource, setIsAddingResource] = useState(false);
  const [editingResource, setEditingResource] = useState<Resource | null>(null);
  const [selectedType, setSelectedType] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");

  const [resources, setResources] = useState<Resource[]>([
    {
      id: 1,
      title: "Getting Started with React Hooks",
      description: "A comprehensive guide to understanding and implementing React Hooks in modern applications.",
      author: "John Smith",
      type: "Tutorial",
      difficultyLevel: "Beginner",
      readingTime: 15,
      contentUrl: "https://example.com/react-hooks-guide",
      isExternal: true,
      featuredImage: "",
      tags: ["React", "JavaScript", "Frontend"],
      publishedDate: "2024-01-15",
      views: 1250,
      downloads: 89,
      featured: true,
      active: true
    },
    {
      id: 2,
      title: "API Design Best Practices",
      description: "Learn how to design robust and scalable APIs with proper documentation and versioning.",
      author: "Sarah Johnson",
      type: "Guide",
      difficultyLevel: "Intermediate",
      readingTime: 25,
      contentUrl: "/docs/api-design.pdf",
      isExternal: false,
      featuredImage: "",
      tags: ["API", "Backend", "Best Practices"],
      publishedDate: "2024-01-10",
      views: 890,
      downloads: 156,
      featured: false,
      active: true
    },
    {
      id: 3,
      title: "Database Optimization Techniques",
      description: "Advanced strategies for optimizing database performance and query efficiency.",
      author: "Mike Chen",
      type: "Video",
      difficultyLevel: "Advanced",
      readingTime: 45,
      contentUrl: "https://youtube.com/watch?v=example",
      isExternal: true,
      featuredImage: "",
      tags: ["Database", "Performance", "SQL"],
      publishedDate: "2024-01-05",
      views: 2100,
      downloads: 0,
      featured: true,
      active: true
    }
  ]);

  const resourceTypes = ["Tutorial", "Guide", "Video", "Template", "Tool", "Case Study"];
  const difficultyLevels = ["Beginner", "Intermediate", "Advanced"];

  const filteredResources = resources.filter(resource => {
    const matchesType = selectedType === "all" || resource.type === selectedType;
    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesType && matchesSearch;
  });

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case "Beginner": return "text-green-600 bg-green-50";
      case "Intermediate": return "text-yellow-600 bg-yellow-50";
      case "Advanced": return "text-red-600 bg-red-50";
      default: return "text-gray-600 bg-gray-50";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Video": return <Video className="w-4 h-4" />;
      case "Tutorial": return <FileText className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const handleSaveResource = (resource: Partial<Resource>) => {
    if (editingResource) {
      setResources(prev => prev.map(r => r.id === editingResource.id ? { ...r, ...resource } : r));
      toast({ title: "Resource Updated", description: "Resource has been successfully updated." });
    } else {
      const newResource: Resource = {
        id: Date.now(),
        title: resource.title || "",
        description: resource.description || "",
        author: resource.author || "",
        type: resource.type || "Tutorial",
        difficultyLevel: resource.difficultyLevel || "Beginner",
        readingTime: resource.readingTime || 5,
        contentUrl: resource.contentUrl || "",
        isExternal: resource.isExternal || false,
        featuredImage: resource.featuredImage || "",
        tags: resource.tags || [],
        publishedDate: new Date().toISOString().split('T')[0],
        views: 0,
        downloads: 0,
        featured: resource.featured || false,
        active: resource.active || true
      };
      setResources(prev => [...prev, newResource]);
      toast({ title: "Resource Added", description: "New resource has been successfully added." });
    }
    setIsAddingResource(false);
    setEditingResource(null);
  };

  const deleteResource = (id: number) => {
    setResources(prev => prev.filter(r => r.id !== id));
    toast({ title: "Resource Deleted", description: "Resource has been removed." });
  };

  const ResourceForm = ({ resource }: { resource?: Resource }) => {
    const [formData, setFormData] = useState({
      title: resource?.title || "",
      description: resource?.description || "",
      author: resource?.author || "",
      type: resource?.type || "Tutorial",
      difficultyLevel: resource?.difficultyLevel || "Beginner",
      readingTime: resource?.readingTime || 5,
      contentUrl: resource?.contentUrl || "",
      isExternal: resource?.isExternal || false,
      tags: resource?.tags?.join(", ") || "",
      featured: resource?.featured || false,
      active: resource?.active ?? true,
    });

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Resource title"
            />
          </div>
          <div>
            <Label htmlFor="author">Author</Label>
            <Input
              id="author"
              value={formData.author}
              onChange={(e) => setFormData(prev => ({ ...prev, author: e.target.value }))}
              placeholder="Author name"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Brief description of the resource"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div>
            <Label htmlFor="type">Type</Label>
            <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {resourceTypes.map(type => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="difficulty">Difficulty</Label>
            <Select value={formData.difficultyLevel} onValueChange={(value) => setFormData(prev => ({ ...prev, difficultyLevel: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {difficultyLevels.map(level => (
                  <SelectItem key={level} value={level}>{level}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="readingTime">Reading Time (minutes)</Label>
            <Input
              id="readingTime"
              type="number"
              min="1"
              value={formData.readingTime}
              onChange={(e) => setFormData(prev => ({ ...prev, readingTime: parseInt(e.target.value) || 5 }))}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="contentUrl">Content URL</Label>
          <Input
            id="contentUrl"
            value={formData.contentUrl}
            onChange={(e) => setFormData(prev => ({ ...prev, contentUrl: e.target.value }))}
            placeholder="https://example.com or /docs/file.pdf"
          />
          <div className="flex items-center space-x-2 mt-2">
            <Switch
              id="isExternal"
              checked={formData.isExternal}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isExternal: checked }))}
            />
            <Label htmlFor="isExternal">External Link</Label>
          </div>
        </div>

        <div>
          <Label htmlFor="tags">Tags (comma-separated)</Label>
          <Input
            id="tags"
            value={formData.tags}
            onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
            placeholder="React, JavaScript, Frontend"
          />
        </div>

        <div>
          <Label>Featured Image</Label>
          <div className="border-2 border-dashed border-border rounded-lg p-4 text-center">
            <Upload className="w-6 h-6 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-2">Upload featured image</p>
            <Button variant="outline" size="sm">Choose File</Button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch
              id="featured"
              checked={formData.featured}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: checked }))}
            />
            <Label htmlFor="featured">Featured Resource</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="active"
              checked={formData.active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, active: checked }))}
            />
            <Label htmlFor="active">Published</Label>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => { setIsAddingResource(false); setEditingResource(null); }}>
            Cancel
          </Button>
          <Button onClick={() => handleSaveResource({ 
            ...formData, 
            tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) 
          })}>
            {resource ? "Update" : "Add"} Resource
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Resources Management</h1>
        <Button onClick={() => setIsAddingResource(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Resource
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-64">
              <Input
                placeholder="Search resources..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="min-w-48">
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {resourceTypes.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Badge variant="secondary">
              {filteredResources.length} resources
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Resources Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredResources.map((resource) => (
          <Card key={resource.id} className={`relative ${!resource.active ? "opacity-60" : ""}`}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-2">
                  {getTypeIcon(resource.type)}
                  <Badge variant="outline">{resource.type}</Badge>
                  {resource.featured && (
                    <Badge className="bg-yellow-100 text-yellow-800">Featured</Badge>
                  )}
                </div>
                <div className="flex space-x-1">
                  {resource.isExternal && (
                    <Button size="sm" variant="outline" asChild>
                      <a href={resource.contentUrl} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </Button>
                  )}
                  <Button size="sm" variant="outline" onClick={() => setEditingResource(resource)}>
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => deleteResource(resource.id)}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <h3 className="font-semibold text-lg mb-2">{resource.title}</h3>
              <p className="text-muted-foreground text-sm mb-4 line-clamp-3">{resource.description}</p>

              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span>Author:</span>
                  <span className="font-medium">{resource.author}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Difficulty:</span>
                  <Badge className={getDifficultyColor(resource.difficultyLevel)}>
                    {resource.difficultyLevel}
                  </Badge>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{resource.readingTime} min</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1">
                      <Eye className="w-4 h-4" />
                      <span>{resource.views}</span>
                    </div>
                    {resource.type !== "Video" && (
                      <div className="flex items-center space-x-1">
                        <Download className="w-4 h-4" />
                        <span>{resource.downloads}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="pt-3 border-t">
                  <div className="flex flex-wrap gap-1">
                    {resource.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="text-xs text-muted-foreground pt-2">
                  Published: {new Date(resource.publishedDate).toLocaleDateString()}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add Resource Dialog */}
      <Dialog open={isAddingResource} onOpenChange={setIsAddingResource}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Resource</DialogTitle>
          </DialogHeader>
          <ResourceForm />
        </DialogContent>
      </Dialog>

      {/* Edit Resource Dialog */}
      <Dialog open={!!editingResource} onOpenChange={() => setEditingResource(null)}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Resource</DialogTitle>
          </DialogHeader>
          {editingResource && <ResourceForm resource={editingResource} />}
        </DialogContent>
      </Dialog>
    </div>
  );
}