import { useState } from "react";
import { Plus, Search, Edit, Trash2, Eye, Star, Settings, Code, Shield, Palette, MoreHorizontal, Image, Calendar } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { Service, getCategoryDisplayName, SERVICE_CATEGORY_CHOICES } from "@/types/service";

// Mock data for services matching backend model
const mockServices: Service[] = [
  {
    id: 1,
    title: "Full Stack Web Development",
    category: "web_development",
    short_description: "Complete web application development from frontend to backend",
    full_description: "We build scalable, modern web applications using cutting-edge technologies like React, Node.js, and cloud platforms. Our full-stack approach ensures seamless integration between frontend and backend systems.",
    icon: "Code",
    image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=500&h=300&fit=crop",
    features: ["React/Vue.js Frontend", "Node.js/Python Backend", "Database Design", "API Development", "Cloud Deployment"],
    price_range: "$15,000 - $50,000",
    duration: "3-6 months",
    is_featured: true,
    is_active: true,
    display_order: 1,
    created_at: "2024-01-15T08:00:00Z",
    updated_at: "2024-01-20T10:30:00Z"
  },
  {
    id: 2,
    title: "Mobile App Development",
    category: "mobile_development",
    short_description: "Native and cross-platform mobile applications",
    full_description: "Build powerful mobile applications for iOS and Android platforms. We use React Native and Flutter for cross-platform development, ensuring your app reaches maximum audience.",
    icon: "Smartphone",
    image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
    features: ["iOS Development", "Android Development", "Cross-platform", "App Store Publishing", "Performance Optimization"],
    price_range: "$20,000 - $75,000",
    duration: "4-8 months",
    is_featured: true,
    is_active: true,
    display_order: 2,
    created_at: "2024-01-10T09:15:00Z",
    updated_at: "2024-01-18T14:45:00Z"
  },
  {
    id: 3,
    title: "UI/UX Design",
    category: "ui_ux_design",
    short_description: "User-centered design and prototyping",
    full_description: "Create intuitive and beautiful user interfaces that drive engagement and conversion. Our design process includes user research, wireframing, prototyping, and usability testing.",
    icon: "Palette",
    image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=500&h=300&fit=crop",
    features: ["User Research", "Wireframing", "Prototyping", "Visual Design", "Usability Testing"],
    price_range: "$5,000 - $20,000",
    duration: "2-4 months",
    is_featured: false,
    is_active: true,
    display_order: 3,
    created_at: "2024-01-05T11:20:00Z",
    updated_at: "2024-01-15T16:10:00Z"
  }
];

// Additional mock services
const additionalMockServices: Service[] = [
  {
    id: 4,
    title: "API Development & Integration",
    category: "custom_software",
    short_description: "RESTful and GraphQL API development with third-party integrations",
    full_description: "RESTful and GraphQL API development with third-party integrations",
    icon: "Code",
    image: null,
    features: ["REST API", "GraphQL", "Third-party Integration", "Documentation"],
    price_range: "$5,000 - $15,000",
    duration: "2-4 weeks",
    is_featured: false,
    is_active: true,
    display_order: 4,
    created_at: "2024-01-08T12:00:00Z",
    updated_at: "2024-01-12T09:30:00Z"
  },
  {
    id: 5,
    title: "Security Audit",
    category: "consulting",
    short_description: "Comprehensive security assessment and vulnerability testing",
    full_description: "Comprehensive security assessment and vulnerability testing",
    icon: "Shield",
    image: null,
    features: ["Vulnerability Assessment", "Penetration Testing", "Security Report", "Recommendations"],
    price_range: "$3,000 - $10,000",
    duration: "1-2 weeks",
    is_featured: false,
    is_active: true,
    display_order: 5,
    created_at: "2024-01-12T14:15:00Z",
    updated_at: "2024-01-16T11:20:00Z"
  },
  {
    id: 6,
    title: "Design System Creation",
    category: "ui_ux_design",
    short_description: "Comprehensive design system with component library",
    full_description: "Comprehensive design system with component library",
    icon: "Palette",
    image: null,
    features: ["Component Library", "Style Guide", "Design Tokens", "Documentation"],
    price_range: "$8,000 - $25,000",
    duration: "4-6 weeks",
    is_featured: false,
    is_active: true,
    display_order: 6,
    created_at: "2024-01-06T10:45:00Z",
    updated_at: "2024-01-14T15:30:00Z"
  },
  {
    id: 7,
    title: "Performance Optimization",
    category: "maintenance",
    short_description: "Application performance analysis and optimization",
    full_description: "Application performance analysis and optimization",
    icon: "Settings",
    image: null,
    features: ["Performance Analysis", "Code Optimization", "Database Tuning", "Monitoring Setup"],
    price_range: "$2,000 - $8,000",
    duration: "1-3 weeks",
    is_featured: false,
    is_active: false,
    display_order: 7,
    created_at: "2024-01-03T16:30:00Z",
    updated_at: "2024-01-10T13:45:00Z"
  }
];

const categoryColors = {
  web_development: "default",
  mobile_development: "secondary",
  ui_ux_design: "outline",
  consulting: "destructive",
  maintenance: "secondary",
  custom_software: "default",
  other: "outline"
} as const;

export default function Services() {
  const [searchTerm, setSearchTerm] = useState("");
  const [services, setServices] = useState<Service[]>([...mockServices, ...additionalMockServices]);
  const [activeTab, setActiveTab] = useState("all");

  const toggleServiceStatus = (id: number) => {
    setServices(prev => prev.map(service =>
      service.id === id ? { ...service, is_active: !service.is_active } : service
    ));
  };

  const toggleFeatured = (id: number) => {
    setServices(prev => prev.map(service =>
      service.id === id ? { ...service, is_featured: !service.is_featured } : service
    ));
  };

  const filteredServices = services.filter(service =>
    service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (service.full_description && service.full_description.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (service.short_description && service.short_description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const featuredServices = filteredServices.filter(service => service.is_featured);
  const activeServices = filteredServices.filter(service => service.is_active);
  const inactiveServices = filteredServices.filter(service => !service.is_active);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const ServiceCard = ({ service }: { service: Service }) => (
    <Card key={service.id} className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <CardTitle className="text-xl">{service.title}</CardTitle>
              {service.is_featured && (
                <Badge variant="default" className="text-xs">
                  <Star className="w-3 h-3 mr-1" />
                  Featured
                </Badge>
              )}
              <Badge variant={service.is_active ? "default" : "secondary"} className="text-xs">
                {service.is_active ? "Active" : "Inactive"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                variant={categoryColors[service.category]}
                className="text-xs w-fit"
              >
                {getCategoryDisplayName(service.category)}
              </Badge>
              {service.image && (
                <Badge variant="outline" className="text-xs">
                  <Image className="w-3 h-3 mr-1" />
                  Has Image
                </Badge>
              )}
            </div>
            <CardDescription>{service.short_description}</CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Edit className="mr-2 h-4 w-4" />
                Edit Service
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => toggleFeatured(service.id)}>
                <Star className="mr-2 h-4 w-4" />
                {service.is_featured ? "Remove from Featured" : "Mark as Featured"}
              </DropdownMenuItem>
              <DropdownMenuItem className="text-destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Service
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {service.image && (
          <div className="w-full h-32 bg-muted rounded-md overflow-hidden">
            <img
              src={service.image}
              alt={service.title}
              className="w-full h-full object-cover"
            />
          </div>
        )}

        <p className="text-sm text-muted-foreground line-clamp-3">
          {service.full_description}
        </p>

        {/* Features */}
        {service.features && service.features.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2">Key Features</h4>
            <div className="flex flex-wrap gap-1">
              {service.features.slice(0, 3).map((feature, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {feature}
                </Badge>
              ))}
              {service.features.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{service.features.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Pricing and Duration */}
        <div className="flex items-center justify-between text-sm">
          <div className="space-y-1">
            {service.price_range && (
              <div className="font-medium text-primary">{service.price_range}</div>
            )}
            {service.duration && (
              <div className="text-muted-foreground">{service.duration}</div>
            )}
          </div>
          <div className="text-right space-y-1">
            <div className="text-xs text-muted-foreground flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              Updated: {formatDate(service.updated_at)}
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">Active</span>
              <Switch
                checked={service.is_active}
                onCheckedChange={() => toggleServiceStatus(service.id)}
                size="sm"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Services Management</h1>
          <p className="text-muted-foreground mt-1">
            Manage your service offerings and pricing
          </p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Add Service
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Services Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All Services ({services.length})</TabsTrigger>
          <TabsTrigger value="featured">Featured ({featuredServices.length})</TabsTrigger>
          <TabsTrigger value="active">Active ({activeServices.length})</TabsTrigger>
          <TabsTrigger value="inactive">Inactive ({inactiveServices.length})</TabsTrigger>
        </TabsList>

        {/* All Services */}
        <TabsContent value="all" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredServices.map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>
        </TabsContent>

        {/* Featured Services */}
        <TabsContent value="featured" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {featuredServices.map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>
        </TabsContent>

        {/* Active Services */}
        <TabsContent value="active" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {activeServices.map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>
        </TabsContent>

        {/* Inactive Services */}
        <TabsContent value="inactive" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {inactiveServices.map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>
        </TabsContent>


      </Tabs>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Services Summary</CardTitle>
          <CardDescription>Overview of your service portfolio</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-foreground">
                {services.length}
              </div>
              <div className="text-sm text-muted-foreground">Total Services</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">
                {services.filter(s => s.is_active).length}
              </div>
              <div className="text-sm text-muted-foreground">Active</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-secondary">
                {services.filter(s => s.is_featured).length}
              </div>
              <div className="text-sm text-muted-foreground">Featured</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-muted-foreground">
                {services.filter(s => !s.is_active).length}
              </div>
              <div className="text-sm text-muted-foreground">Inactive</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-accent">
                {new Set(services.map(s => s.category)).size}
              </div>
              <div className="text-sm text-muted-foreground">Categories</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}