import { useState } from "react";
import { Plus, Search, Edit, Trash2, Eye, Star, Settings, Code, Shield, Palette, MoreH<PERSON>zontal } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";

// Mock data for main services
const mainServices = [
  {
    id: 1,
    title: "Full Stack Web Development",
    shortDescription: "Complete web application development from frontend to backend",
    description: "We build scalable, modern web applications using cutting-edge technologies like React, Node.js, and cloud platforms. Our full-stack approach ensures seamless integration between frontend and backend systems.",
    icon: "Code",
    features: ["React/Vue.js Frontend", "Node.js/Python Backend", "Database Design", "API Development", "Cloud Deployment"],
    priceRange: "$15,000 - $50,000",
    duration: "3-6 months",
    isFeatured: true,
    isActive: true,
    displayOrder: 1
  },
  {
    id: 2,
    title: "Mobile App Development",
    shortDescription: "Native and cross-platform mobile applications",
    description: "Build powerful mobile applications for iOS and Android platforms. We use React Native and Flutter for cross-platform development, ensuring your app reaches maximum audience.",
    icon: "Smartphone",
    features: ["iOS Development", "Android Development", "Cross-platform", "App Store Publishing", "Performance Optimization"],
    priceRange: "$20,000 - $75,000",
    duration: "4-8 months",
    isFeatured: true,
    isActive: true,
    displayOrder: 2
  },
  {
    id: 3,
    title: "UI/UX Design",
    shortDescription: "User-centered design and prototyping",
    description: "Create intuitive and beautiful user interfaces that drive engagement and conversion. Our design process includes user research, wireframing, prototyping, and usability testing.",
    icon: "Palette",
    features: ["User Research", "Wireframing", "Prototyping", "Visual Design", "Usability Testing"],
    priceRange: "$5,000 - $20,000",
    duration: "2-4 months",
    isFeatured: false,
    isActive: true,
    displayOrder: 3
  }
];

// Mock data for additional services
const additionalServices = [
  {
    id: 4,
    title: "API Development & Integration",
    category: "Backend",
    description: "RESTful and GraphQL API development with third-party integrations",
    priceRange: "$5,000 - $15,000",
    duration: "2-4 weeks",
    isActive: true
  },
  {
    id: 5,
    title: "Security Audit",
    category: "Security",
    description: "Comprehensive security assessment and vulnerability testing",
    priceRange: "$3,000 - $10,000",
    duration: "1-2 weeks",
    isActive: true
  },
  {
    id: 6,
    title: "Design System Creation",
    category: "UI/UX",
    description: "Comprehensive design system with component library",
    priceRange: "$8,000 - $25,000",
    duration: "4-6 weeks",
    isActive: true
  },
  {
    id: 7,
    title: "Performance Optimization",
    category: "Other",
    description: "Application performance analysis and optimization",
    priceRange: "$2,000 - $8,000",
    duration: "1-3 weeks",
    isActive: false
  }
];

const categoryColors = {
  Backend: "default",
  Security: "destructive",
  "UI/UX": "secondary",
  Other: "outline"
} as const;

export default function Services() {
  const [searchTerm, setSearchTerm] = useState("");
  const [services, setServices] = useState(mainServices);
  const [additionalSvcs, setAdditionalSvcs] = useState(additionalServices);
  const [activeTab, setActiveTab] = useState("main");

  const toggleServiceStatus = (id: number, isMain: boolean = true) => {
    if (isMain) {
      setServices(prev => prev.map(service =>
        service.id === id ? { ...service, isActive: !service.isActive } : service
      ));
    } else {
      setAdditionalSvcs(prev => prev.map(service =>
        service.id === id ? { ...service, isActive: !service.isActive } : service
      ));
    }
  };

  const toggleFeatured = (id: number) => {
    setServices(prev => prev.map(service =>
      service.id === id ? { ...service, isFeatured: !service.isFeatured } : service
    ));
  };

  const filteredMainServices = services.filter(service =>
    service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredAdditionalServices = additionalSvcs.filter(service =>
    service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Services Management</h1>
          <p className="text-muted-foreground mt-1">
            Manage your service offerings and pricing
          </p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Add Service
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Services Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="main">Main Services ({services.length})</TabsTrigger>
          <TabsTrigger value="additional">Additional Services ({additionalSvcs.length})</TabsTrigger>
        </TabsList>

        {/* Main Services */}
        <TabsContent value="main" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredMainServices.map((service) => (
              <Card key={service.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CardTitle className="text-xl">{service.title}</CardTitle>
                        {service.isFeatured && (
                          <Badge variant="default" className="text-xs">
                            <Star className="w-3 h-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                        <Badge variant={service.isActive ? "default" : "secondary"} className="text-xs">
                          {service.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <CardDescription>{service.shortDescription}</CardDescription>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Service
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => toggleFeatured(service.id)}>
                          <Star className="mr-2 h-4 w-4" />
                          {service.isFeatured ? "Remove from Featured" : "Mark as Featured"}
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Service
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {service.description}
                  </p>

                  {/* Features */}
                  <div>
                    <h4 className="text-sm font-medium mb-2">Key Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {service.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {service.features.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{service.features.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Pricing and Duration */}
                  <div className="grid grid-cols-2 gap-4 pt-2 border-t border-border">
                    <div>
                      <div className="text-xs text-muted-foreground">Price Range</div>
                      <div className="text-sm font-medium">{service.priceRange}</div>
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">Duration</div>
                      <div className="text-sm font-medium">{service.duration}</div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-2 border-t border-border">
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">Active</span>
                      <Switch
                        checked={service.isActive}
                        onCheckedChange={() => toggleServiceStatus(service.id, true)}
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">Featured</span>
                      <Switch
                        checked={service.isFeatured}
                        onCheckedChange={() => toggleFeatured(service.id)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Additional Services */}
        <TabsContent value="additional" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredAdditionalServices.map((service) => (
              <Card key={service.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CardTitle className="text-lg">{service.title}</CardTitle>
                        <Badge variant={service.isActive ? "default" : "secondary"} className="text-xs">
                          {service.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <Badge 
                        variant={categoryColors[service.category as keyof typeof categoryColors]} 
                        className="text-xs w-fit"
                      >
                        {service.category}
                      </Badge>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Service
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Service
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    {service.description}
                  </p>

                  {/* Pricing and Duration */}
                  <div className="space-y-2 pt-2 border-t border-border">
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">Price Range</span>
                      <span className="text-xs font-medium">{service.priceRange}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">Duration</span>
                      <span className="text-xs font-medium">{service.duration}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-2 border-t border-border">
                    <span className="text-xs text-muted-foreground">Active</span>
                    <Switch
                      checked={service.isActive}
                      onCheckedChange={() => toggleServiceStatus(service.id, false)}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Services Summary</CardTitle>
          <CardDescription>Overview of your service portfolio</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-foreground">
                {services.length + additionalSvcs.length}
              </div>
              <div className="text-sm text-muted-foreground">Total Services</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">
                {services.filter(s => s.isActive).length + additionalSvcs.filter(s => s.isActive).length}
              </div>
              <div className="text-sm text-muted-foreground">Active</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-secondary">
                {services.filter(s => s.isFeatured).length}
              </div>
              <div className="text-sm text-muted-foreground">Featured</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-foreground">{services.length}</div>
              <div className="text-sm text-muted-foreground">Main Services</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-foreground">{additionalSvcs.length}</div>
              <div className="text-sm text-muted-foreground">Additional</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}