import { useState, useMemo } from "react";
import { Search, Filter, Calendar, Mail, Phone, Trash2, Eye, Building, User, Clock } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { DemoRequestDetailModal } from "@/components/admin/DemoRequestDetailModal";

// TypeScript interface matching the database schema
interface DemoRequest {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  company_name: string;
  job_title?: string;
  company_size?: string;
  industry?: string;
  project_type: string;
  budget_range?: string;
  timeline?: string;
  project_description: string;
  specific_requirements?: string;
  preferred_demo_date?: string;
  preferred_demo_time?: string;
  how_did_you_hear?: string;
  status: string;
  assigned_to?: string;
  demo_scheduled_at?: string;
  notes?: string;
  created_at: string;
}

// TODO: Replace with API call when backend is ready
// TODO: Implement server-side pagination (e.g., /api/demo-requests?page=1&limit=20)
// TODO: Move filtering to backend for better performance
// TODO: Add loading states and error handling
// Mock data matching database schema
const demoRequests: DemoRequest[] = [
  {
    id: 1,
    first_name: "Sarah",
    last_name: "Johnson",
    email: "<EMAIL>",
    phone: "+****************",
    company_name: "TechCorp Inc.",
    job_title: "CTO",
    company_size: "50-200",
    industry: "Technology",
    project_type: "E-commerce Platform",
    budget_range: "$50,000 - $100,000",
    timeline: "3-6 months",
    project_description: "Need a complete e-commerce solution with inventory management and payment processing",
    specific_requirements: "Must integrate with existing ERP system, support multiple currencies",
    preferred_demo_date: "2024-01-25",
    preferred_demo_time: "2:00 PM",
    how_did_you_hear: "Google Search",
    status: "pending",
    assigned_to: null,
    demo_scheduled_at: null,
    notes: "High priority client, very interested in our solutions",
    created_at: "2024-01-15T10:30:00Z"
  },
  {
    id: 2,
    first_name: "Michael",
    last_name: "Chen",
    email: "<EMAIL>",
    phone: "+****************",
    company_name: "StartupXYZ",
    job_title: "Product Manager",
    company_size: "10-50",
    industry: "Food & Beverage",
    project_type: "Mobile App",
    budget_range: "$25,000 - $50,000",
    timeline: "2-4 months",
    project_description: "iOS and Android app for food delivery service with real-time tracking",
    specific_requirements: "GPS tracking, payment gateway integration, push notifications",
    preferred_demo_date: "2024-01-28",
    preferred_demo_time: "10:00 AM",
    how_did_you_hear: "LinkedIn",
    status: "scheduled",
    assigned_to: "John Smith",
    demo_scheduled_at: "2024-01-28T10:00:00Z",
    notes: "Demo scheduled, sent calendar invite",
    created_at: "2024-01-12T14:20:00Z"
  },
  {
    id: 3,
    first_name: "Emma",
    last_name: "Wilson",
    email: "<EMAIL>",
    phone: "+****************",
    company_name: "DataFlow Solutions",
    job_title: "VP of Engineering",
    company_size: "200-500",
    industry: "Data Analytics",
    project_type: "Analytics Dashboard",
    budget_range: "$75,000 - $150,000",
    timeline: "4-8 months",
    project_description: "Real-time analytics dashboard for business intelligence with custom reporting",
    specific_requirements: "API integrations, custom charts, role-based access control",
    preferred_demo_date: "2024-01-22",
    preferred_demo_time: "3:00 PM",
    how_did_you_hear: "Referral",
    status: "completed",
    assigned_to: "Alice Brown",
    demo_scheduled_at: "2024-01-22T15:00:00Z",
    notes: "Demo completed successfully, follow-up scheduled for next week",
    created_at: "2024-01-08T09:15:00Z"
  },
  {
    id: 4,
    first_name: "Robert",
    last_name: "Davis",
    email: "<EMAIL>",
    phone: "+****************",
    company_name: "CloudTech Ltd.",
    job_title: "IT Director",
    company_size: "500+",
    industry: "Cloud Services",
    project_type: "Cloud Migration",
    budget_range: "$100,000+",
    timeline: "6-12 months",
    project_description: "Complete cloud migration strategy and implementation for legacy systems",
    specific_requirements: "AWS expertise, minimal downtime, security compliance",
    preferred_demo_date: "2024-02-05",
    preferred_demo_time: "11:00 AM",
    how_did_you_hear: "Conference",
    status: "in-progress",
    assigned_to: "John Smith",
    demo_scheduled_at: "2024-02-05T11:00:00Z",
    notes: "Technical demo in progress, very promising lead",
    created_at: "2024-01-20T16:45:00Z"
  }
];

const statusOptions = [
  { value: "all", label: "All Statuses" },
  { value: "new", label: "New" },
  { value: "pending", label: "Pending" },
  { value: "scheduled", label: "Scheduled" },
  { value: "in-progress", label: "In Progress" },
  { value: "completed", label: "Completed" },
  { value: "cancelled", label: "Cancelled" }
];

const budgetRanges = [
  { value: "all", label: "All Budgets" },
  { value: "under-25k", label: "Under $25k" },
  { value: "25k-50k", label: "$25k - $50k" },
  { value: "50k-100k", label: "$50k - $100k" },
  { value: "75k-150k", label: "$75k - $150k" },
  { value: "100k-plus", label: "$100k+" }
];

const companySizeOptions = [
  { value: "all", label: "All Company Sizes" },
  { value: "1-10", label: "1-10 employees" },
  { value: "10-50", label: "10-50 employees" },
  { value: "50-200", label: "50-200 employees" },
  { value: "200-500", label: "200-500 employees" },
  { value: "500+", label: "500+ employees" }
];

const industryOptions = [
  { value: "all", label: "All Industries" },
  { value: "Technology", label: "Technology" },
  { value: "Healthcare", label: "Healthcare" },
  { value: "Finance", label: "Finance" },
  { value: "E-commerce", label: "E-commerce" },
  { value: "Food & Beverage", label: "Food & Beverage" },
  { value: "Data Analytics", label: "Data Analytics" },
  { value: "Cloud Services", label: "Cloud Services" },
  { value: "Manufacturing", label: "Manufacturing" },
  { value: "Education", label: "Education" },
  { value: "Other", label: "Other" }
];

const getStatusBadge = (status: string) => {
  const variants = {
    new: "secondary",
    pending: "secondary",
    scheduled: "default",
    "in-progress": "outline",
    completed: "default",
    cancelled: "destructive"
  } as const;

  return (
    <Badge variant={variants[status as keyof typeof variants] || "secondary"}>
      {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
    </Badge>
  );
};

export default function DemoRequests() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [budgetFilter, setBudgetFilter] = useState("all");
  const [companySizeFilter, setCompanySizeFilter] = useState("all");
  const [industryFilter, setIndustryFilter] = useState("all");
  const [selectedRequest, setSelectedRequest] = useState<DemoRequest | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // IMMEDIATE IMPROVEMENT: Add client-side pagination to limit DOM elements
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20); // Fixed page size for now

  // TODO: Add loading and error states for API calls
  // const [isLoading, setIsLoading] = useState(false);
  // const [error, setError] = useState<string | null>(null);

  // TODO: Replace client-side filtering with server-side filtering for better performance
  // TODO: Implement debounced search to avoid filtering on every keystroke
  // TODO: Move this logic to a custom hook for reusability
  // PERFORMANCE IMPROVEMENT: Use useMemo to prevent unnecessary re-filtering on every render
  const filteredRequests = useMemo(() => {
    return demoRequests.filter(request => {
      const fullName = `${request.first_name} ${request.last_name}`;
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = searchTerm === "" ||
        fullName.toLowerCase().includes(searchLower) ||
        request.company_name.toLowerCase().includes(searchLower) ||
        request.email.toLowerCase().includes(searchLower) ||
        request.project_type.toLowerCase().includes(searchLower) ||
        (request.job_title && request.job_title.toLowerCase().includes(searchLower)) ||
        (request.project_description && request.project_description.toLowerCase().includes(searchLower)) ||
        (request.industry && request.industry.toLowerCase().includes(searchLower)) ||
        (request.assigned_to && request.assigned_to.toLowerCase().includes(searchLower));

      const matchesStatus = statusFilter === "all" || request.status === statusFilter;
      const matchesBudget = budgetFilter === "all" || request.budget_range === budgetFilter;
      const matchesCompanySize = companySizeFilter === "all" || request.company_size === companySizeFilter;
      const matchesIndustry = industryFilter === "all" || request.industry === industryFilter;

      return matchesSearch && matchesStatus && matchesBudget && matchesCompanySize && matchesIndustry;
    });
  }, [searchTerm, statusFilter, budgetFilter, companySizeFilter, industryFilter]);

  // IMMEDIATE IMPROVEMENT: Add client-side pagination to reduce DOM elements
  const paginatedRequests = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredRequests.slice(startIndex, endIndex);
  }, [filteredRequests, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredRequests.length / pageSize);

  // Reset to first page when filters change
  const resetPagination = () => {
    setCurrentPage(1);
  };

  const handleViewDetails = (request: DemoRequest) => {
    setSelectedRequest(request);
    setIsDetailModalOpen(true);
  };

  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedRequest(null);
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setBudgetFilter("all");
    setCompanySizeFilter("all");
    setIndustryFilter("all");
    resetPagination(); // Reset to first page when clearing filters
  };

  const hasActiveFilters = searchTerm !== "" || statusFilter !== "all" || budgetFilter !== "all" || companySizeFilter !== "all" || industryFilter !== "all";

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">Demo Requests</h1>
        <p className="text-muted-foreground mt-1">
          Manage and track demo requests from potential clients
        </p>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, company, email, or project type..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  resetPagination(); // Reset to first page when searching
                }}
                className="pl-10"
              />
            </div>
            <div className="flex flex-col gap-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Select value={statusFilter} onValueChange={(value) => {
                  setStatusFilter(value);
                  resetPagination();
                }}>
                  <SelectTrigger>
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={budgetFilter} onValueChange={(value) => {
                  setBudgetFilter(value);
                  resetPagination();
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by budget" />
                  </SelectTrigger>
                  <SelectContent>
                    {budgetRanges.map((range) => (
                      <SelectItem key={range.value} value={range.value}>
                        {range.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={companySizeFilter} onValueChange={(value) => {
                  setCompanySizeFilter(value);
                  resetPagination();
                }}>
                  <SelectTrigger>
                    <Building className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Filter by company size" />
                  </SelectTrigger>
                  <SelectContent>
                    {companySizeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={industryFilter} onValueChange={(value) => {
                  setIndustryFilter(value);
                  resetPagination();
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by industry" />
                  </SelectTrigger>
                  <SelectContent>
                    {industryOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {hasActiveFilters && (
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    {filteredRequests.length} of {demoRequests.length} requests shown
                  </p>
                  <Button variant="outline" size="sm" onClick={handleClearFilters}>
                    Clear Filters
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <Card>
        <CardHeader>
          <CardTitle>Demo Requests ({filteredRequests.length})</CardTitle>
          <CardDescription>
            Showing {paginatedRequests.length} of {filteredRequests.length} filtered results ({demoRequests.length} total)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* TODO: Add loading skeleton when implementing API calls */}
          {/* TODO: Add empty state when no results found */}
          {/* TODO: Consider virtual scrolling for large datasets */}
          {/* TODO: Add sorting functionality to table headers */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Contact Information</TableHead>
                <TableHead>Company Details</TableHead>
                <TableHead>Project Details</TableHead>
                <TableHead>Budget & Timeline</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Demo Information</TableHead>
                <TableHead>Assigned To</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedRequests.map((request) => (
                <TableRow key={request.id} className="hover:bg-muted/50">
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{request.first_name} {request.last_name}</div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <Mail className="w-3 h-3" />
                        {request.email}
                      </div>
                      {request.phone && (
                        <div className="text-xs text-muted-foreground flex items-center gap-1">
                          <Phone className="w-3 h-3" />
                          {request.phone}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium flex items-center gap-1">
                        <Building className="w-3 h-3" />
                        {request.company_name}
                      </div>
                      {request.job_title && (
                        <div className="text-xs text-muted-foreground flex items-center gap-1">
                          <User className="w-3 h-3" />
                          {request.job_title}
                        </div>
                      )}
                      {request.industry && (
                        <div className="text-xs text-muted-foreground">
                          {request.industry}
                        </div>
                      )}
                      {request.company_size && (
                        <div className="text-xs text-muted-foreground">
                          {request.company_size} employees
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{request.project_type}</div>
                      <div className="text-sm text-muted-foreground line-clamp-2">
                        {request.project_description}
                      </div>
                      {request.specific_requirements && (
                        <div className="text-xs text-muted-foreground">
                          Requirements: {request.specific_requirements.substring(0, 50)}...
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {request.budget_range && (
                        <div className="text-sm font-medium">{request.budget_range}</div>
                      )}
                      {request.timeline && (
                        <div className="text-sm text-muted-foreground">{request.timeline}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(request.status)}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {request.preferred_demo_date && (
                        <div className="text-sm font-medium flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {new Date(request.preferred_demo_date).toLocaleDateString()}
                        </div>
                      )}
                      {request.preferred_demo_time && (
                        <div className="text-xs text-muted-foreground flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {request.preferred_demo_time}
                        </div>
                      )}
                      <div className="text-xs text-muted-foreground">
                        Created: {new Date(request.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {request.assigned_to || (
                        <span className="text-muted-foreground">Unassigned</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewDetails(request)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Calendar className="mr-2 h-4 w-4" />
                          Schedule Demo
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Request
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* IMMEDIATE IMPROVEMENT: Client-side pagination to reduce DOM elements */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between px-2 py-4">
              <div className="text-sm text-muted-foreground">
                Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, filteredRequests.length)} of {filteredRequests.length} results
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {/* Show page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          onClick={() => setCurrentPage(pageNum)}
                          isActive={currentPage === pageNum}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detail Modal */}
      <DemoRequestDetailModal
        request={selectedRequest}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
      />
    </div>
  );
}