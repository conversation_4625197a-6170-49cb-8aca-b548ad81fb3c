import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Play,
  Pause,
  RotateCcw,
  Zap,
  Loader2,
  Heart,
  Star,
  Sparkles,
  Waves,
  MessageSquare,
  Zap as ZapIcon,
  Move,
  RotateCw
} from "lucide-react";

// ChatGPT Skeleton Component
function ChatGPTSkeleton({ className = '' }) {
  return <div className={`bg-gray-200 rounded animate-pulse ${className}`} />;
}

// Your custom Skeleton component for testing
function CustomSkeleton({ className = '' }) {
  return <div className={`bg-gray-200 rounded animate-pulse ${className}`} />;
}

export default function AnimationTest() {
  const [isAnimating, setIsAnimating] = useState(true);
  const [selectedDemo, setSelectedDemo] = useState<string | null>(null);
  const [showFramerDemo, setShowFramerDemo] = useState(true);
  const [framerVariant, setFramerVariant] = useState<string>("fadeIn");

  const toggleAnimation = () => {
    setIsAnimating(!isAnimating);
  };

  const resetAnimations = () => {
    setIsAnimating(false);
    setTimeout(() => setIsAnimating(true), 100);
  };

  // Framer Motion variants
  const framerVariants = {
    fadeIn: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.5 }
    },
    slideUp: {
      initial: { y: 50, opacity: 0 },
      animate: { y: 0, opacity: 1 },
      exit: { y: -50, opacity: 0 },
      transition: { duration: 0.6 }
    },
    scale: {
      initial: { scale: 0.8, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      exit: { scale: 0.8, opacity: 0 },
      transition: { duration: 0.4 }
    },
    rotate: {
      initial: { rotate: -180, opacity: 0 },
      animate: { rotate: 0, opacity: 1 },
      exit: { rotate: 180, opacity: 0 },
      transition: { duration: 0.7 }
    },
    bounce: {
      initial: { y: -100, opacity: 0 },
      animate: { y: 0, opacity: 1 },
      exit: { y: 100, opacity: 0 },
      transition: { type: "spring" as const, stiffness: 300, damping: 20 }
    }
  };

  const toggleFramerDemo = () => {
    setShowFramerDemo(!showFramerDemo);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Animation Testing</h1>
          <p className="text-muted-foreground mt-1">
            Test and preview various animations and loading states
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={toggleAnimation}
            className="flex items-center gap-2"
          >
            {isAnimating ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            {isAnimating ? 'Pause' : 'Play'} Animations
          </Button>
          <Button 
            variant="outline" 
            onClick={resetAnimations}
            className="flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            Reset
          </Button>
        </div>
      </div>

      {/* ChatGPT Skeleton Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            ChatGPT Skeleton Animation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">Original ChatGPT Example</h3>
            <div className="space-y-3 p-4 bg-muted/30 rounded-lg">
              <ChatGPTSkeleton className="h-4 w-3/4 mb-2" />
              <ChatGPTSkeleton className="h-3 w-1/2" />
            </div>
            <div className="mt-4 text-sm text-muted-foreground">
              <p><strong>Component Code:</strong></p>
              <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-x-auto">
{`export function Skeleton({ className = '' }) {
  return <div className={\`bg-gray-200 rounded animate-pulse \${className}\`} />;
}

// Usage:
<Skeleton className="h-4 w-3/4 mb-2" />
<Skeleton className="h-3 w-1/2" />`}
              </pre>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Multiple Examples</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3 p-4 bg-muted/30 rounded-lg">
                <h4 className="text-sm font-medium">Text Loading</h4>
                <ChatGPTSkeleton className="h-4 w-full mb-2" />
                <ChatGPTSkeleton className="h-4 w-4/5 mb-2" />
                <ChatGPTSkeleton className="h-4 w-3/5" />
              </div>

              <div className="space-y-3 p-4 bg-muted/30 rounded-lg">
                <h4 className="text-sm font-medium">Card Content</h4>
                <ChatGPTSkeleton className="h-6 w-2/3 mb-3" />
                <ChatGPTSkeleton className="h-3 w-full mb-1" />
                <ChatGPTSkeleton className="h-3 w-5/6 mb-1" />
                <ChatGPTSkeleton className="h-3 w-3/4" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Skeleton Loading Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="w-5 h-5" />
            Skeleton Loading States
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Skeleton Examples */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Basic Skeleton Components</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </div>
          </div>

          <Separator />

          {/* Card Skeleton */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Card Loading State</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="p-4">
                  <div className="space-y-3">
                    <Skeleton className="h-[200px] w-full rounded-md" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                    <div className="flex justify-between">
                      <Skeleton className="h-8 w-20" />
                      <Skeleton className="h-8 w-16" />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Custom Animation Examples */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5" />
            Custom Animations
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Pulse Animation */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Pulse Effects</h3>
            <div className="flex gap-4 items-center">
              <div className={`w-16 h-16 bg-primary rounded-full ${isAnimating ? 'animate-pulse' : ''}`} />
              <div className={`w-16 h-16 bg-secondary rounded-lg ${isAnimating ? 'animate-pulse' : ''}`} />
              <div className={`w-16 h-16 bg-accent rounded-md ${isAnimating ? 'animate-pulse' : ''}`} />
            </div>
          </div>

          <Separator />

          {/* Spin Animation */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Spin Effects</h3>
            <div className="flex gap-4 items-center">
              <Loader2 className={`w-8 h-8 text-primary ${isAnimating ? 'animate-spin' : ''}`} />
              <Zap className={`w-8 h-8 text-yellow-500 ${isAnimating ? 'animate-spin' : ''}`} />
              <Star className={`w-8 h-8 text-blue-500 ${isAnimating ? 'animate-spin' : ''}`} />
            </div>
          </div>

          <Separator />

          {/* Bounce Animation */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Bounce Effects</h3>
            <div className="flex gap-4 items-center">
              <Heart className={`w-8 h-8 text-red-500 ${isAnimating ? 'animate-bounce' : ''}`} />
              <div className={`w-8 h-8 bg-green-500 rounded-full ${isAnimating ? 'animate-bounce' : ''}`} />
              <Badge className={`${isAnimating ? 'animate-bounce' : ''}`}>Bouncing Badge</Badge>
            </div>
          </div>

          <Separator />

          {/* Custom CSS Animations */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Custom Animations</h3>
            <div className="space-y-4">
              <div className="flex gap-4 items-center">
                <div 
                  className={`w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg ${
                    isAnimating ? 'animate-pulse' : ''
                  }`}
                  style={{
                    animation: isAnimating ? 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite' : 'none'
                  }}
                />
                <div 
                  className={`w-16 h-4 bg-gradient-to-r from-blue-500 to-green-500 rounded-full ${
                    isAnimating ? 'animate-pulse' : ''
                  }`}
                  style={{
                    animation: isAnimating ? 'pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite' : 'none'
                  }}
                />
              </div>
              
              <div className="text-sm text-muted-foreground">
                These elements use the built-in Tailwind CSS pulse animation with different timing.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interactive Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Waves className="w-5 h-5" />
            Interactive Animation Demo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['pulse', 'spin', 'bounce', 'ping'].map((animation) => (
              <Button
                key={animation}
                variant={selectedDemo === animation ? "default" : "outline"}
                onClick={() => setSelectedDemo(selectedDemo === animation ? null : animation)}
                className="h-20 flex flex-col gap-2"
              >
                <div 
                  className={`w-6 h-6 bg-current rounded ${
                    selectedDemo === animation && isAnimating ? `animate-${animation}` : ''
                  }`}
                />
                <span className="capitalize">{animation}</span>
              </Button>
            ))}
          </div>
          
          {selectedDemo && (
            <div className="mt-6 p-4 bg-muted rounded-lg">
              <p className="text-sm">
                <strong>Selected:</strong> {selectedDemo} animation
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Click the button again to stop the animation, or use the controls above.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Framer Motion Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Move className="w-5 h-5" />
            Framer Motion Animations
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Animation Controls */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              onClick={toggleFramerDemo}
              className="flex items-center gap-2"
            >
              <ZapIcon className="w-4 h-4" />
              {showFramerDemo ? 'Hide' : 'Show'} Demo
            </Button>

            <div className="flex gap-2">
              {Object.keys(framerVariants).map((variant) => (
                <Button
                  key={variant}
                  variant={framerVariant === variant ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    setFramerVariant(variant);
                    setShowFramerDemo(false);
                    setTimeout(() => setShowFramerDemo(true), 100);
                  }}
                  className="capitalize"
                >
                  {variant}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Animation Examples */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Basic Motion Example */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Basic Motion</h3>
              <div className="h-32 bg-muted/30 rounded-lg flex items-center justify-center">
                <AnimatePresence mode="wait">
                  {showFramerDemo && (
                    <motion.div
                      key={`basic-${framerVariant}`}
                      className="w-16 h-16 bg-primary rounded-lg"
                      {...framerVariants[framerVariant as keyof typeof framerVariants]}
                    />
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* Card Animation */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Card Animation</h3>
              <div className="h-32 bg-muted/30 rounded-lg p-4">
                <AnimatePresence mode="wait">
                  {showFramerDemo && (
                    <motion.div
                      key={`card-${framerVariant}`}
                      className="bg-card border rounded-lg p-3 shadow-sm"
                      {...framerVariants[framerVariant as keyof typeof framerVariants]}
                    >
                      <div className="text-sm font-medium">Animated Card</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        With Framer Motion
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* Text Animation */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Text Animation</h3>
              <div className="h-32 bg-muted/30 rounded-lg flex items-center justify-center">
                <AnimatePresence mode="wait">
                  {showFramerDemo && (
                    <motion.div
                      key={`text-${framerVariant}`}
                      className="text-center"
                      {...framerVariants[framerVariant as keyof typeof framerVariants]}
                    >
                      <div className="text-lg font-bold">Hello!</div>
                      <div className="text-sm text-muted-foreground">Framer Motion</div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>

          <Separator />

          {/* Advanced Examples */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Advanced Animations</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Stagger Animation */}
              <div className="space-y-3">
                <h4 className="font-medium">Stagger Animation</h4>
                <div className="bg-muted/30 rounded-lg p-4">
                  <AnimatePresence mode="wait">
                    {showFramerDemo && (
                      <motion.div
                        key={`stagger-${framerVariant}`}
                        initial="initial"
                        animate="animate"
                        exit="exit"
                        variants={{
                          initial: {},
                          animate: {
                            transition: {
                              staggerChildren: 0.1
                            }
                          },
                          exit: {}
                        }}
                        className="flex gap-2"
                      >
                        {[1, 2, 3, 4].map((i) => (
                          <motion.div
                            key={i}
                            variants={framerVariants[framerVariant as keyof typeof framerVariants]}
                            className="w-8 h-8 bg-secondary rounded"
                          />
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              {/* Hover Animation */}
              <div className="space-y-3">
                <h4 className="font-medium">Hover Animation</h4>
                <div className="bg-muted/30 rounded-lg p-4 flex justify-center">
                  <motion.div
                    className="w-16 h-16 bg-accent rounded-lg cursor-pointer"
                    whileHover={{
                      scale: 1.1,
                      rotate: 5,
                      transition: { duration: 0.2 }
                    }}
                    whileTap={{ scale: 0.95 }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Code Example */}
          <div className="mt-6">
            <h4 className="font-medium mb-2">Current Animation Code:</h4>
            <pre className="text-xs bg-muted p-3 rounded overflow-x-auto">
{`<motion.div
  initial={{ ${Object.entries(framerVariants[framerVariant as keyof typeof framerVariants].initial).map(([key, value]) => `${key}: ${value}`).join(', ')} }}
  animate={{ ${Object.entries(framerVariants[framerVariant as keyof typeof framerVariants].animate).map(([key, value]) => `${key}: ${value}`).join(', ')} }}
  exit={{ ${Object.entries(framerVariants[framerVariant as keyof typeof framerVariants].exit).map(([key, value]) => `${key}: ${value}`).join(', ')} }}
  transition={{ ${Object.entries(framerVariants[framerVariant as keyof typeof framerVariants].transition).map(([key, value]) => `${key}: ${typeof value === 'string' ? `"${value}"` : value}`).join(', ')} }}
>
  Your content here
</motion.div>`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
