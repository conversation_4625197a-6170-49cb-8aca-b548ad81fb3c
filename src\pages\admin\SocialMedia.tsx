import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  Save, Plus, Edit, Trash2, ExternalLink, 
  Facebook, Twitter, Instagram, Linkedin, 
  Github, Youtube, Globe, Mail 
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface SocialLink {
  id: number;
  platform: string;
  name: string;
  url: string;
  username: string;
  followers: number;
  active: boolean;
  displayOrder: number;
  icon: any;
  color: string;
}

export default function SocialMedia() {
  const { toast } = useToast();
  const [isAddingLink, setIsAddingLink] = useState(false);
  const [editingL<PERSON>, setEditingLink] = useState<SocialLink | null>(null);

  const [socialLinks, setSocialLinks] = useState<SocialLink[]>([
    {
      id: 1,
      platform: "LinkedIn",
      name: "TechFlow Solutions",
      url: "https://linkedin.com/company/techflow-solutions",
      username: "@techflow-solutions",
      followers: 1250,
      active: true,
      displayOrder: 1,
      icon: Linkedin,
      color: "text-blue-600"
    },
    {
      id: 2,
      platform: "GitHub",
      name: "TechFlow Solutions",
      url: "https://github.com/techflow-solutions",
      username: "@techflow-solutions",
      followers: 890,
      active: true,
      displayOrder: 2,
      icon: Github,
      color: "text-gray-900"
    },
    {
      id: 3,
      platform: "Twitter",
      name: "TechFlow Solutions",
      url: "https://twitter.com/techflow_dev",
      username: "@techflow_dev",
      followers: 2100,
      active: true,
      displayOrder: 3,
      icon: Twitter,
      color: "text-blue-400"
    },
    {
      id: 4,
      platform: "YouTube",
      name: "TechFlow Solutions",
      url: "https://youtube.com/@techflow-solutions",
      username: "@techflow-solutions",
      followers: 450,
      active: false,
      displayOrder: 4,
      icon: Youtube,
      color: "text-red-600"
    }
  ]);

  const platformOptions = [
    { name: "LinkedIn", icon: Linkedin, color: "text-blue-600" },
    { name: "GitHub", icon: Github, color: "text-gray-900" },
    { name: "Twitter", icon: Twitter, color: "text-blue-400" },
    { name: "Facebook", icon: Facebook, color: "text-blue-600" },
    { name: "Instagram", icon: Instagram, color: "text-pink-600" },
    { name: "YouTube", icon: Youtube, color: "text-red-600" },
    { name: "Website", icon: Globe, color: "text-gray-600" },
    { name: "Email", icon: Mail, color: "text-gray-600" }
  ];

  const handleSaveLink = (link: Partial<SocialLink>) => {
    if (editingLink) {
      setSocialLinks(prev => prev.map(l => l.id === editingLink.id ? { ...l, ...link } : l));
      toast({ title: "Social Link Updated", description: "Social media link has been successfully updated." });
    } else {
      const platform = platformOptions.find(p => p.name === link.platform);
      const newLink: SocialLink = {
        id: Date.now(),
        platform: link.platform || "",
        name: link.name || "",
        url: link.url || "",
        username: link.username || "",
        followers: link.followers || 0,
        active: link.active || true,
        displayOrder: socialLinks.length + 1,
        icon: platform?.icon || Globe,
        color: platform?.color || "text-gray-600"
      };
      setSocialLinks(prev => [...prev, newLink]);
      toast({ title: "Social Link Added", description: "New social media link has been successfully added." });
    }
    setIsAddingLink(false);
    setEditingLink(null);
  };

  const deleteLink = (id: number) => {
    setSocialLinks(prev => prev.filter(l => l.id !== id));
    toast({ title: "Social Link Deleted", description: "Social media link has been removed." });
  };

  const updateFollowers = () => {
    toast({ 
      title: "Followers Updated", 
      description: "Follower counts have been refreshed from external APIs." 
    });
  };

  const SocialLinkForm = ({ link }: { link?: SocialLink }) => {
    const [formData, setFormData] = useState({
      platform: link?.platform || "",
      name: link?.name || "",
      url: link?.url || "",
      username: link?.username || "",
      followers: link?.followers || 0,
      active: link?.active ?? true,
    });

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="platform">Platform</Label>
            <select
              id="platform"
              value={formData.platform}
              onChange={(e) => setFormData(prev => ({ ...prev, platform: e.target.value }))}
              className="w-full p-2 border rounded-md"
            >
              <option value="">Select Platform</option>
              {platformOptions.map(platform => (
                <option key={platform.name} value={platform.name}>{platform.name}</option>
              ))}
            </select>
          </div>
          <div>
            <Label htmlFor="name">Display Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., TechFlow Solutions"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="url">URL</Label>
          <Input
            id="url"
            value={formData.url}
            onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
            placeholder="https://platform.com/username"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="username">Username/Handle</Label>
            <Input
              id="username"
              value={formData.username}
              onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
              placeholder="@username"
            />
          </div>
          <div>
            <Label htmlFor="followers">Followers Count</Label>
            <Input
              id="followers"
              type="number"
              value={formData.followers}
              onChange={(e) => setFormData(prev => ({ ...prev, followers: parseInt(e.target.value) || 0 }))}
              placeholder="0"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="active"
            checked={formData.active}
            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, active: checked }))}
          />
          <Label htmlFor="active">Active (Display on website)</Label>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => { setIsAddingLink(false); setEditingLink(null); }}>
            Cancel
          </Button>
          <Button onClick={() => handleSaveLink(formData)}>
            {link ? "Update" : "Add"} Link
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Social Media Management</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={updateFollowers}>
            Refresh Followers
          </Button>
          <Button onClick={() => setIsAddingLink(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Social Link
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {socialLinks.filter(link => link.active).length}
              </div>
              <div className="text-sm text-muted-foreground">Active Platforms</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {socialLinks.reduce((sum, link) => sum + link.followers, 0).toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Total Followers</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {Math.max(...socialLinks.map(link => link.followers)).toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Largest Following</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {Math.round(socialLinks.reduce((sum, link) => sum + link.followers, 0) / socialLinks.length).toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Average Followers</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Social Links Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {socialLinks.map((link) => {
          const Icon = link.icon;
          return (
            <Card key={link.id} className={`relative ${!link.active ? "opacity-60" : ""}`}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg bg-accent ${link.color}`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{link.platform}</h3>
                      <p className="text-sm text-muted-foreground">{link.username}</p>
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    <Button size="sm" variant="outline" asChild>
                      <a href={link.url} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => setEditingLink(link)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => deleteLink(link.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <p className="font-medium">{link.name}</p>
                    <p className="text-sm text-muted-foreground truncate">{link.url}</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-center">
                      <div className="text-lg font-bold">{link.followers.toLocaleString()}</div>
                      <div className="text-xs text-muted-foreground">Followers</div>
                    </div>
                    <div className="flex space-x-2">
                      <Badge variant={link.active ? "secondary" : "outline"}>
                        {link.active ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>

                  <div className="pt-3 border-t">
                    <div className="text-xs text-muted-foreground">
                      Display Order: #{link.displayOrder}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Website Integration Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Website Integration Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-6 bg-accent/20 rounded-lg">
            <h3 className="text-lg font-semibold mb-4 text-center">Follow Us</h3>
            <div className="flex justify-center space-x-4">
              {socialLinks
                .filter(link => link.active)
                .sort((a, b) => a.displayOrder - b.displayOrder)
                .map((link) => {
                  const Icon = link.icon;
                  return (
                    <a
                      key={link.id}
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`p-3 rounded-full bg-background shadow-md hover:shadow-lg transition-shadow ${link.color}`}
                      title={`${link.name} - ${link.followers.toLocaleString()} followers`}
                    >
                      <Icon className="w-5 h-5" />
                    </a>
                  );
                })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Social Link Dialog */}
      <Dialog open={isAddingLink} onOpenChange={setIsAddingLink}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Social Media Link</DialogTitle>
          </DialogHeader>
          <SocialLinkForm />
        </DialogContent>
      </Dialog>

      {/* Edit Social Link Dialog */}
      <Dialog open={!!editingLink} onOpenChange={() => setEditingLink(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Social Media Link</DialogTitle>
          </DialogHeader>
          {editingLink && <SocialLinkForm link={editingLink} />}
        </DialogContent>
      </Dialog>
    </div>
  );
}