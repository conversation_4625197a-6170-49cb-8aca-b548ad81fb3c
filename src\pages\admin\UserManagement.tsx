import { useState, use<PERSON>emo } from "react";
import { <PERSON>, <PERSON>H<PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Plus, Edit, Trash2, Shield, User, Users, 
  Eye, Clock, Activity, AlertCircle, CheckCircle 
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface AdminUser {
  id: number;
  name: string;
  email: string;
  role: string;
  permissions: string[];
  active: boolean;
  lastLogin: string;
  createdAt: string;
  avatar?: string;
}

interface ActivityLog {
  id: number;
  userId: number;
  userName: string;
  action: string;
  details: string;
  timestamp: string;
  ip: string;
}

export default function UserManagement() {
  const { toast } = useToast();
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [editingUser, setEditingUser] = useState<AdminUser | null>(null);
  const [selectedRole, setSelectedRole] = useState<string>("all");

  // TODO: Add search functionality with debounced input
  // const [searchTerm, setSearchTerm] = useState("");

  // TODO: Add pagination when user list grows large
  // const [currentPage, setCurrentPage] = useState(1);
  // const [pageSize] = useState(10);

  // TODO: Replace with API call when backend is ready
  // TODO: Implement server-side filtering and pagination for large user bases
  // TODO: Add loading states and error handling
  const [users, setUsers] = useState<AdminUser[]>([
    {
      id: 1,
      name: "John Smith",
      email: "<EMAIL>",
      role: "Super Admin",
      permissions: ["all"],
      active: true,
      lastLogin: "2024-01-15T10:30:00Z",
      createdAt: "2023-06-15T08:00:00Z"
    },
    {
      id: 2,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      role: "Content Manager",
      permissions: ["content_edit", "team_manage", "services_manage"],
      active: true,
      lastLogin: "2024-01-14T16:45:00Z",
      createdAt: "2023-08-20T09:15:00Z"
    },
    {
      id: 3,
      name: "Mike Chen",
      email: "<EMAIL>",
      role: "Editor",
      permissions: ["content_edit", "resources_manage"],
      active: false,
      lastLogin: "2024-01-10T14:20:00Z",
      createdAt: "2023-10-05T11:30:00Z"
    }
  ]);

  // TODO: Replace with API call and implement pagination for activity logs
  // TODO: Add date filtering and search functionality for logs
  const [activityLogs] = useState<ActivityLog[]>([
    {
      id: 1,
      userId: 1,
      userName: "John Smith",
      action: "User Created",
      details: "Created new user: Sarah Johnson",
      timestamp: "2024-01-15T10:30:00Z",
      ip: "*************"
    },
    {
      id: 2,
      userId: 2,
      userName: "Sarah Johnson",
      action: "Content Updated",
      details: "Updated About Us page content",
      timestamp: "2024-01-14T16:45:00Z",
      ip: "*************"
    },
    {
      id: 3,
      userId: 2,
      userName: "Sarah Johnson",
      action: "Team Member Added",
      details: "Added new team member: Alex Rodriguez",
      timestamp: "2024-01-14T14:20:00Z",
      ip: "*************"
    },
    {
      id: 4,
      userId: 1,
      userName: "John Smith",
      action: "Service Updated",
      details: "Modified Web Development service pricing",
      timestamp: "2024-01-13T09:15:00Z",
      ip: "*************"
    }
  ]);

  const roles = [
    {
      name: "Super Admin",
      permissions: ["all"],
      description: "Full access to all features"
    },
    {
      name: "Content Manager",
      permissions: ["content_edit", "team_manage", "services_manage", "resources_manage"],
      description: "Manage content and team members"
    },
    {
      name: "Editor",
      permissions: ["content_edit", "resources_manage"],
      description: "Edit content and manage resources"
    },
    {
      name: "Viewer",
      permissions: ["content_view"],
      description: "Read-only access"
    }
  ];

  const allPermissions = [
    "content_edit",
    "content_view",
    "team_manage",
    "services_manage",
    "resources_manage",
    "demo_manage",
    "analytics_view",
    "user_manage",
    "settings_manage"
  ];

  // PERFORMANCE IMPROVEMENT: Use useMemo to prevent unnecessary re-filtering on every render
  const filteredUsers = useMemo(() => {
    return selectedRole === "all"
      ? users
      : users.filter(user => user.role === selectedRole);
  }, [users, selectedRole]);

  const getRoleColor = (role: string) => {
    switch (role) {
      case "Super Admin": return "text-red-600 bg-red-50";
      case "Content Manager": return "text-blue-600 bg-blue-50";
      case "Editor": return "text-green-600 bg-green-50";
      case "Viewer": return "text-gray-600 bg-gray-50";
      default: return "text-gray-600 bg-gray-50";
    }
  };

  const handleSaveUser = (user: Partial<AdminUser>) => {
    if (editingUser) {
      setUsers(prev => prev.map(u => u.id === editingUser.id ? { ...u, ...user } : u));
      toast({ title: "User Updated", description: "User has been successfully updated." });
    } else {
      const newUser: AdminUser = {
        id: Date.now(),
        name: user.name || "",
        email: user.email || "",
        role: user.role || "Viewer",
        permissions: user.permissions || ["content_view"],
        active: user.active || true,
        lastLogin: new Date().toISOString(),
        createdAt: new Date().toISOString()
      };
      setUsers(prev => [...prev, newUser]);
      toast({ title: "User Added", description: "New user has been successfully created." });
    }
    setIsAddingUser(false);
    setEditingUser(null);
  };

  const deleteUser = (id: number) => {
    setUsers(prev => prev.filter(u => u.id !== id));
    toast({ title: "User Deleted", description: "User has been removed from the system." });
  };

  const UserForm = ({ user }: { user?: AdminUser }) => {
    const [formData, setFormData] = useState({
      name: user?.name || "",
      email: user?.email || "",
      role: user?.role || "Viewer",
      permissions: user?.permissions || ["content_view"],
      active: user?.active ?? true,
    });

    const selectedRoleData = roles.find(r => r.name === formData.role);

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="John Doe"
            />
          </div>
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="role">Role</Label>
          <Select 
            value={formData.role} 
            onValueChange={(value) => {
              const roleData = roles.find(r => r.name === value);
              setFormData(prev => ({ 
                ...prev, 
                role: value,
                permissions: roleData?.permissions || ["content_view"]
              }));
            }}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {roles.map(role => (
                <SelectItem key={role.name} value={role.name}>
                  <div>
                    <div>{role.name}</div>
                    <div className="text-xs text-muted-foreground">{role.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {selectedRoleData && (
            <p className="text-sm text-muted-foreground mt-1">{selectedRoleData.description}</p>
          )}
        </div>

        <div>
          <Label>Permissions</Label>
          <div className="grid grid-cols-2 gap-2 mt-2 p-3 border rounded-md bg-accent/5">
            {selectedRoleData?.permissions.includes("all") ? (
              <div className="col-span-2 text-center py-2">
                <Badge className="bg-red-100 text-red-800">Full Access - All Permissions</Badge>
              </div>
            ) : (
              selectedRoleData?.permissions.map(permission => (
                <Badge key={permission} variant="secondary" className="text-xs">
                  {permission.replace('_', ' ').toUpperCase()}
                </Badge>
              ))
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="active"
            checked={formData.active}
            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, active: checked }))}
          />
          <Label htmlFor="active">Active User</Label>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => { setIsAddingUser(false); setEditingUser(null); }}>
            Cancel
          </Button>
          <Button onClick={() => handleSaveUser(formData)}>
            {user ? "Update" : "Create"} User
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">User Management</h1>
        <Button onClick={() => setIsAddingUser(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add User
        </Button>
      </div>

      <Tabs defaultValue="users" className="space-y-6">
        <TabsList>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
          <TabsTrigger value="activity">Activity Log</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          {/* User Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <Users className="w-8 h-8 text-blue-600" />
                  <div>
                    <div className="text-2xl font-bold">{users.length}</div>
                    <div className="text-sm text-muted-foreground">Total Users</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                  <div>
                    <div className="text-2xl font-bold">{users.filter(u => u.active).length}</div>
                    <div className="text-sm text-muted-foreground">Active Users</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <Shield className="w-8 h-8 text-purple-600" />
                  <div>
                    <div className="text-2xl font-bold">{users.filter(u => u.role === "Super Admin").length}</div>
                    <div className="text-sm text-muted-foreground">Admins</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <Activity className="w-8 h-8 text-orange-600" />
                  <div>
                    <div className="text-2xl font-bold">
                      {users.filter(u => {
                        const lastLogin = new Date(u.lastLogin);
                        const now = new Date();
                        const daysDiff = (now.getTime() - lastLogin.getTime()) / (1000 * 3600 * 24);
                        return daysDiff <= 7;
                      }).length}
                    </div>
                    <div className="text-sm text-muted-foreground">Active This Week</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Users Filter */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Label>Filter by Role</Label>
                  <Select value={selectedRole} onValueChange={setSelectedRole}>
                    <SelectTrigger className="w-64">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Roles</SelectItem>
                      {roles.map(role => (
                        <SelectItem key={role.name} value={role.name}>{role.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Badge variant="secondary">
                  {filteredUsers.length} users
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>Admin Users</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                            <User className="w-4 h-4" />
                          </div>
                          <div>
                            <div className="font-medium">{user.name}</div>
                            <div className="text-sm text-muted-foreground">{user.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRoleColor(user.role)}>
                          {user.role}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.active ? "secondary" : "outline"}>
                          {user.active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(user.lastLogin).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(user.lastLogin).toLocaleTimeString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        {new Date(user.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button size="sm" variant="outline" onClick={() => setEditingUser(user)}>
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => deleteUser(user.id)}>
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {roles.map((role) => (
              <Card key={role.name}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="w-5 h-5" />
                    <span>{role.name}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">{role.description}</p>
                  <div className="space-y-2">
                    <Label>Permissions:</Label>
                    <div className="flex flex-wrap gap-2">
                      {role.permissions.includes("all") ? (
                        <Badge className="bg-red-100 text-red-800">All Permissions</Badge>
                      ) : (
                        role.permissions.map(permission => (
                          <Badge key={permission} variant="secondary">
                            {permission.replace('_', ' ').toUpperCase()}
                          </Badge>
                        ))
                      )}
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t">
                    <div className="text-sm text-muted-foreground">
                      Users with this role: {users.filter(u => u.role === role.name).length}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-5 h-5" />
                <span>Recent Activity</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activityLogs.map((log) => (
                  <div key={log.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                    <div className="w-8 h-8 bg-accent rounded-full flex items-center justify-center mt-1">
                      <User className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{log.userName}</span>
                        <Badge variant="outline" className="text-xs">{log.action}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{log.details}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{new Date(log.timestamp).toLocaleString()}</span>
                        </div>
                        <div>IP: {log.ip}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add User Dialog */}
      <Dialog open={isAddingUser} onOpenChange={setIsAddingUser}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
          </DialogHeader>
          <UserForm />
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={!!editingUser} onOpenChange={() => setEditingUser(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          {editingUser && <UserForm user={editingUser} />}
        </DialogContent>
      </Dialog>
    </div>
  );
}