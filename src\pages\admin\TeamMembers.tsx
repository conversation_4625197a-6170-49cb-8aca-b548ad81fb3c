import { useState, useMemo } from "react";
import { Plus, Search, Edit, Trash2, Eye, Upload, Github, Linkedin, MoreHorizontal, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>oot<PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { TeamMember, CreateTeamMember, UpdateTeamMember } from "@/types/team";
import { TeamMemberForm } from "@/components/admin/TeamMemberForm";
import { TeamMemberViewModal } from "@/components/admin/TeamMemberViewModal";
import { toast } from "sonner";

// TODO: Replace with API call when backend is ready
// TODO: Implement server-side pagination (e.g., /api/team-members?page=1&limit=12)
// TODO: Move filtering to backend for better performance
// TODO: Add loading states and error handling
// Mock data matching database schema
const teamMembers: TeamMember[] = [
  {
    id: 1,
    full_name: "John Smith",
    position: "Senior Full Stack Developer",
    email: "<EMAIL>",
    bio: "Experienced developer with 8+ years in React, Node.js, and cloud technologies. Passionate about clean code and scalable architectures.",
    profile_image: "/api/placeholder/64/64",
    is_active: true,
    display_order: 1,
    linkedin_url: "https://linkedin.com/in/johnsmith",
    github_url: "https://github.com/johnsmith",
    created_at: "2022-01-15T08:00:00Z",
    updated_at: "2024-01-15T10:30:00Z"
  },
  {
    id: 2,
    full_name: "Alice Brown",
    position: "UI/UX Designer",
    email: "<EMAIL>",
    bio: "Creative designer focused on user-centered design and modern interfaces. Expert in Figma, Adobe Creative Suite, and prototyping.",
    profile_image: "/api/placeholder/64/64",
    is_active: true,
    display_order: 2,
    linkedin_url: "https://linkedin.com/in/alicebrown",
    github_url: null,
    created_at: "2022-03-20T09:15:00Z",
    updated_at: "2024-01-14T16:45:00Z"
  },
  {
    id: 3,
    full_name: "Michael Johnson",
    position: "DevOps Engineer",
    email: "<EMAIL>",
    bio: "Infrastructure specialist with expertise in AWS, Docker, Kubernetes, and CI/CD pipelines. Ensuring reliable and scalable deployments.",
    profile_image: "/api/placeholder/64/64",
    is_active: true,
    display_order: 3,
    linkedin_url: "https://linkedin.com/in/michaeljohnson",
    github_url: "https://github.com/michaeljohnson",
    created_at: "2021-11-10T11:30:00Z",
    updated_at: "2024-01-10T14:20:00Z"
  },
  {
    id: 4,
    full_name: "Sarah Wilson",
    position: "Project Manager",
    email: "<EMAIL>",
    bio: "Agile project management expert with a track record of delivering complex software projects on time and within budget.",
    profile_image: "/api/placeholder/64/64",
    is_active: false,
    display_order: 4,
    linkedin_url: "https://linkedin.com/in/sarahwilson",
    github_url: null,
    created_at: "2023-01-08T12:00:00Z",
    updated_at: "2024-01-08T15:30:00Z"
  }
];

export default function TeamMembers() {
  const [searchTerm, setSearchTerm] = useState("");
  const [members, setMembers] = useState(teamMembers);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
  const [memberToDelete, setMemberToDelete] = useState<TeamMember | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // IMMEDIATE IMPROVEMENT: Add client-side pagination to limit DOM elements
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12); // 12 cards per page (3x4 grid)

  // TODO: Add loading and error states for API calls
  // const [isApiLoading, setIsApiLoading] = useState(false);
  // const [error, setError] = useState<string | null>(null);

  // TODO: Replace client-side filtering with server-side filtering for better performance
  // TODO: Implement debounced search to avoid filtering on every keystroke
  // TODO: Move this logic to a custom hook for reusability
  // PERFORMANCE IMPROVEMENT: Use useMemo to prevent unnecessary re-filtering on every render
  const filteredMembers = useMemo(() => {
    return members.filter(member =>
      member.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (member.email && member.email.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [members, searchTerm]);

  // IMMEDIATE IMPROVEMENT: Add client-side pagination to reduce DOM elements
  const paginatedMembers = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredMembers.slice(startIndex, endIndex);
  }, [filteredMembers, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredMembers.length / pageSize);

  // Reset to first page when search changes
  const resetPagination = () => {
    setCurrentPage(1);
  };

  // PERFORMANCE IMPROVEMENT: Memoize summary calculations to avoid recalculating on every render
  const teamSummary = useMemo(() => {
    const totalMembers = members.length;
    const activeMembers = members.filter(m => m.is_active).length;
    const inactiveMembers = members.filter(m => !m.is_active).length;
    const uniquePositions = new Set(members.map(m => m.position.toLowerCase())).size;

    return {
      total: totalMembers,
      active: activeMembers,
      inactive: inactiveMembers,
      positions: uniquePositions
    };
  }, [members]);

  const toggleMemberStatus = (id: number) => {
    setMembers(prev => prev.map(member =>
      member.id === id ? {
        ...member,
        is_active: !member.is_active,
        updated_at: new Date().toISOString()
      } : member
    ));
  };

  const getSocialIcon = (platform: string) => {
    const icons = {
      linkedin: Linkedin,
      github: Github
    };
    return icons[platform as keyof typeof icons];
  };

  const handleAddMember = async (data: CreateTeamMember) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newMember: TeamMember = {
        id: Math.max(...members.map(m => m.id)) + 1,
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      setMembers(prev => [...prev, newMember]);
      setIsAddModalOpen(false);
      toast.success("Team member added successfully!");
    } catch (error) {
      toast.error("Failed to add team member. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditMember = async (data: UpdateTeamMember) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setMembers(prev => prev.map(member =>
        member.id === data.id
          ? { ...member, ...data, updated_at: new Date().toISOString() }
          : member
      ));

      setIsEditModalOpen(false);
      setSelectedMember(null);
      toast.success("Team member updated successfully!");
    } catch (error) {
      toast.error("Failed to update team member. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewMember = (member: TeamMember) => {
    setSelectedMember(member);
    setIsViewModalOpen(true);
  };

  const handleEditClick = (member: TeamMember) => {
    setSelectedMember(member);
    setIsEditModalOpen(true);
  };

  const handleDeleteClick = (member: TeamMember) => {
    setMemberToDelete(member);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!memberToDelete) return;

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setMembers(prev => prev.filter(member => member.id !== memberToDelete.id));
      toast.success("Team member removed successfully!");
      setIsDeleteDialogOpen(false);
      setMemberToDelete(null);
    } catch (error) {
      toast.error("Failed to remove team member. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-6 relative">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="flex items-center gap-2 bg-card p-4 rounded-lg shadow-lg border">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span className="text-sm font-medium">Processing...</span>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Team Members</h1>
          <p className="text-muted-foreground mt-1">
            Manage your team member profiles and information
          </p>
        </div>
        <Button
          onClick={() => setIsAddModalOpen(true)}
          className="hover:scale-105 transition-transform duration-200"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Team Member
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search team members..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                resetPagination(); // Reset to first page when searching
              }}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Team Members Grid */}
      {/* TODO: Add loading skeleton when implementing API calls */}
      {/* TODO: Consider virtual scrolling for very large teams */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {paginatedMembers.map((member, index) => (
          <Card
            key={member.id}
            className="hover:shadow-md transition-shadow"
          >
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center space-y-4">
                {/* Avatar and Status */}
                <div className="relative">
                  <Avatar className="w-20 h-20">
                    <AvatarImage src={member.profile_image || "/api/placeholder/64/64"} alt={member.full_name} />
                    <AvatarFallback className="text-lg">
                      {member.full_name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-1 -right-1">
                    <Badge variant={member.is_active ? "default" : "secondary"} className="text-xs">
                      {member.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>

                {/* Basic Info */}
                <div className="space-y-2">
                  <h3 className="font-semibold text-lg text-foreground">{member.full_name}</h3>
                  <p className="text-sm text-primary font-medium">{member.position}</p>
                  {member.email && <p className="text-xs text-muted-foreground">{member.email}</p>}
                </div>

                {/* Bio */}
                {member.bio && (
                  <p className="text-sm text-muted-foreground line-clamp-3 text-left">
                    {member.bio}
                  </p>
                )}

                {/* Social Links */}
                <div className="flex items-center gap-2">
                  {member.linkedin_url && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 hover:scale-110 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
                      asChild
                    >
                      <a href={member.linkedin_url} target="_blank" rel="noopener noreferrer">
                        <Linkedin className="w-4 h-4" />
                      </a>
                    </Button>
                  )}
                  {member.github_url && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 hover:scale-110 hover:bg-gray-50 hover:text-gray-800 transition-all duration-200"
                      asChild
                    >
                      <a href={member.github_url} target="_blank" rel="noopener noreferrer">
                        <Github className="w-4 h-4" />
                      </a>
                    </Button>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between w-full pt-4 border-t border-border">
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">Active</span>
                    <Switch
                      checked={member.is_active}
                      onCheckedChange={() => toggleMemberStatus(member.id)}
                    />
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewMember(member)}>
                        <Eye className="mr-2 h-4 w-4" />
                        View Profile
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditClick(member)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Member
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-destructive"
                        onClick={() => handleDeleteClick(member)}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Remove Member
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* IMMEDIATE IMPROVEMENT: Client-side pagination to reduce DOM elements */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-2 py-4">
          <div className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, filteredMembers.length)} of {filteredMembers.length} members
          </div>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>

              {/* Show page numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      onClick={() => setCurrentPage(pageNum)}
                      isActive={currentPage === pageNum}
                      className="cursor-pointer"
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                );
              })}

              <PaginationItem>
                <PaginationNext
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {/* Empty State */}
      {filteredMembers.length === 0 && (
        <Card className="animate-in fade-in slide-in-from-bottom-4 duration-500">
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center mb-4 animate-pulse">
                <Search className="w-8 h-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-foreground">No team members found</h3>
              <p className="text-muted-foreground">
                {searchTerm ? "Try adjusting your search criteria" : "Get started by adding your first team member"}
              </p>
              {!searchTerm && (
                <Button
                  className="mt-4 hover:scale-105 transition-transform duration-200"
                  onClick={() => setIsAddModalOpen(true)}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Team Member
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary */}
      <Card className="animate-in fade-in slide-in-from-bottom-4 duration-700">
        <CardHeader>
          <CardTitle>Team Summary</CardTitle>
          <CardDescription>Overview of your team composition</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-foreground">{teamSummary.total}</div>
              <div className="text-sm text-muted-foreground">Total Members</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">
                {teamSummary.active}
              </div>
              <div className="text-sm text-muted-foreground">Active</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-secondary">
                {teamSummary.inactive}
              </div>
              <div className="text-sm text-muted-foreground">Inactive</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-foreground">
                {teamSummary.positions}
              </div>
              <div className="text-sm text-muted-foreground">Roles</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Team Member Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Team Member</DialogTitle>
            <DialogDescription>
              Fill in the details to add a new team member to your organization.
            </DialogDescription>
          </DialogHeader>
          <TeamMemberForm
            onSubmit={handleAddMember}
            onCancel={() => setIsAddModalOpen(false)}
            isLoading={isLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Team Member Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Team Member</DialogTitle>
            <DialogDescription>
              Update the team member's information below.
            </DialogDescription>
          </DialogHeader>
          {selectedMember && (
            <TeamMemberForm
              member={selectedMember}
              onSubmit={handleEditMember}
              onCancel={() => {
                setIsEditModalOpen(false);
                setSelectedMember(null);
              }}
              isLoading={isLoading}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Team Member Modal */}
      <TeamMemberViewModal
        member={selectedMember}
        isOpen={isViewModalOpen}
        onClose={() => {
          setIsViewModalOpen(false);
          setSelectedMember(null);
        }}
        onEdit={(member) => {
          setIsViewModalOpen(false);
          setSelectedMember(member);
          setIsEditModalOpen(true);
        }}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Team Member</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove <strong>{memberToDelete?.full_name}</strong> from the team?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setMemberToDelete(null);
              }}
              disabled={isLoading}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              disabled={isLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isLoading ? "Removing..." : "Remove Member"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}