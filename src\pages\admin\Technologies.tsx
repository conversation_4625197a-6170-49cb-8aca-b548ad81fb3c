import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Edit, Trash2, Upload, Code, Database, Smartphone, Globe, Settings, Star } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Technology {
  id: number;
  name: string;
  description: string;
  category: string;
  proficiencyLevel: string;
  yearsExperience: number;
  logoUrl: string;
  featured: boolean;
  active: boolean;
  displayOrder: number;
}

interface Category {
  id: number;
  name: string;
  description: string;
  icon: any;
  color: string;
  displayOrder: number;
}

export default function Technologies() {
  const { toast } = useToast();
  const [selectedCategory, setSelectedCategory] = useState<number>(1);
  const [isAddingTech, setIsAddingTech] = useState(false);
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [editingTech, setEditingTech] = useState<Technology | null>(null);

  const [categories] = useState<Category[]>([
    { id: 1, name: "Frontend", description: "UI/UX Technologies", icon: Globe, color: "blue", displayOrder: 1 },
    { id: 2, name: "Backend", description: "Server Technologies", icon: Database, color: "green", displayOrder: 2 },
    { id: 3, name: "Mobile", description: "Mobile Development", icon: Smartphone, color: "purple", displayOrder: 3 },
    { id: 4, name: "DevOps", description: "Infrastructure & Tools", icon: Settings, color: "orange", displayOrder: 4 },
  ]);

  const [technologies, setTechnologies] = useState<Technology[]>([
    { id: 1, name: "React", description: "Modern JavaScript library for building user interfaces", category: "Frontend", proficiencyLevel: "Expert", yearsExperience: 5, logoUrl: "", featured: true, active: true, displayOrder: 1 },
    { id: 2, name: "TypeScript", description: "Typed superset of JavaScript", category: "Frontend", proficiencyLevel: "Expert", yearsExperience: 4, logoUrl: "", featured: true, active: true, displayOrder: 2 },
    { id: 3, name: "Node.js", description: "JavaScript runtime for server-side development", category: "Backend", proficiencyLevel: "Advanced", yearsExperience: 5, logoUrl: "", featured: true, active: true, displayOrder: 1 },
    { id: 4, name: "PostgreSQL", description: "Advanced open-source relational database", category: "Backend", proficiencyLevel: "Advanced", yearsExperience: 6, logoUrl: "", featured: false, active: true, displayOrder: 2 },
    { id: 5, name: "React Native", description: "Mobile app development framework", category: "Mobile", proficiencyLevel: "Intermediate", yearsExperience: 3, logoUrl: "", featured: false, active: true, displayOrder: 1 },
    { id: 6, name: "Docker", description: "Containerization platform", category: "DevOps", proficiencyLevel: "Advanced", yearsExperience: 4, logoUrl: "", featured: true, active: true, displayOrder: 1 },
  ]);

  const filteredTechnologies = technologies.filter(tech => 
    tech.category === categories.find(cat => cat.id === selectedCategory)?.name
  );

  const getProficiencyColor = (level: string) => {
    switch (level) {
      case "Expert": return "text-green-600 bg-green-50";
      case "Advanced": return "text-blue-600 bg-blue-50";
      case "Intermediate": return "text-yellow-600 bg-yellow-50";
      case "Beginner": return "text-gray-600 bg-gray-50";
      default: return "text-gray-600 bg-gray-50";
    }
  };

  const handleSaveTechnology = (tech: Partial<Technology>) => {
    if (editingTech) {
      setTechnologies(prev => prev.map(t => t.id === editingTech.id ? { ...t, ...tech } : t));
      toast({ title: "Technology Updated", description: "Technology has been successfully updated." });
    } else {
      const newTech: Technology = {
        id: Date.now(),
        name: tech.name || "",
        description: tech.description || "",
        category: categories.find(cat => cat.id === selectedCategory)?.name || "",
        proficiencyLevel: tech.proficiencyLevel || "Beginner",
        yearsExperience: tech.yearsExperience || 1,
        logoUrl: tech.logoUrl || "",
        featured: tech.featured || false,
        active: tech.active || true,
        displayOrder: technologies.length + 1
      };
      setTechnologies(prev => [...prev, newTech]);
      toast({ title: "Technology Added", description: "New technology has been successfully added." });
    }
    setIsAddingTech(false);
    setEditingTech(null);
  };

  const deleteTechnology = (id: number) => {
    setTechnologies(prev => prev.filter(t => t.id !== id));
    toast({ title: "Technology Deleted", description: "Technology has been removed." });
  };

  const TechnologyForm = ({ technology }: { technology?: Technology }) => {
    const [formData, setFormData] = useState({
      name: technology?.name || "",
      description: technology?.description || "",
      proficiencyLevel: technology?.proficiencyLevel || "Beginner",
      yearsExperience: technology?.yearsExperience || 1,
      logoUrl: technology?.logoUrl || "",
      featured: technology?.featured || false,
      active: technology?.active ?? true,
    });

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="techName">Technology Name</Label>
            <Input
              id="techName"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., React"
            />
          </div>
          <div>
            <Label htmlFor="proficiency">Proficiency Level</Label>
            <Select value={formData.proficiencyLevel} onValueChange={(value) => setFormData(prev => ({ ...prev, proficiencyLevel: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Beginner">Beginner</SelectItem>
                <SelectItem value="Intermediate">Intermediate</SelectItem>
                <SelectItem value="Advanced">Advanced</SelectItem>
                <SelectItem value="Expert">Expert</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Brief description of the technology"
            rows={3}
          />
        </div>

        <div>
          <Label htmlFor="experience">Years of Experience</Label>
          <Input
            id="experience"
            type="number"
            min="0"
            max="20"
            value={formData.yearsExperience}
            onChange={(e) => setFormData(prev => ({ ...prev, yearsExperience: parseInt(e.target.value) || 0 }))}
          />
        </div>

        <div>
          <Label>Technology Logo</Label>
          <div className="border-2 border-dashed border-border rounded-lg p-4 text-center">
            <Upload className="w-6 h-6 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-2">Upload technology logo</p>
            <Button variant="outline" size="sm">Choose File</Button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch
              id="featured"
              checked={formData.featured}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: checked }))}
            />
            <Label htmlFor="featured">Featured Technology</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="active"
              checked={formData.active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, active: checked }))}
            />
            <Label htmlFor="active">Active</Label>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => { setIsAddingTech(false); setEditingTech(null); }}>
            Cancel
          </Button>
          <Button onClick={() => handleSaveTechnology(formData)}>
            {technology ? "Update" : "Add"} Technology
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Technologies Management</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setIsAddingCategory(true)}>
            Add Category
          </Button>
          <Button onClick={() => setIsAddingTech(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Technology
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <Card>
          <CardHeader>
            <CardTitle>Categories</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full p-3 rounded-lg text-left transition-colors ${
                    selectedCategory === category.id
                      ? "bg-primary/10 text-primary border border-primary/20"
                      : "hover:bg-accent"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <Icon className="w-5 h-5" />
                    <div>
                      <div className="font-medium">{category.name}</div>
                      <div className="text-xs text-muted-foreground">{category.description}</div>
                    </div>
                  </div>
                </button>
              );
            })}
          </CardContent>
        </Card>

        {/* Technologies Grid */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {categories.find(cat => cat.id === selectedCategory)?.name} Technologies
                <Badge variant="secondary">
                  {filteredTechnologies.length} technologies
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {filteredTechnologies.map((tech) => (
                  <Card key={tech.id} className={`relative ${!tech.active ? "opacity-60" : ""}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                            <Code className="w-5 h-5" />
                          </div>
                          <div>
                            <h3 className="font-semibold">{tech.name}</h3>
                            {tech.featured && (
                              <Star className="w-4 h-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingTech(tech)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => deleteTechnology(tech.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      <p className="text-sm text-muted-foreground mb-3">{tech.description}</p>

                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Proficiency:</span>
                          <Badge className={getProficiencyColor(tech.proficiencyLevel)}>
                            {tech.proficiencyLevel}
                          </Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Experience:</span>
                          <span className="text-sm font-medium">{tech.yearsExperience} years</span>
                        </div>
                      </div>

                      <div className="flex justify-between items-center mt-4 pt-3 border-t">
                        <div className="flex space-x-2">
                          {tech.featured && (
                            <Badge variant="secondary" className="text-xs">Featured</Badge>
                          )}
                          <Badge variant={tech.active ? "secondary" : "outline"} className="text-xs">
                            {tech.active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Add Technology Dialog */}
      <Dialog open={isAddingTech} onOpenChange={setIsAddingTech}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Technology</DialogTitle>
          </DialogHeader>
          <TechnologyForm />
        </DialogContent>
      </Dialog>

      {/* Edit Technology Dialog */}
      <Dialog open={!!editingTech} onOpenChange={() => setEditingTech(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Technology</DialogTitle>
          </DialogHeader>
          {editingTech && <TechnologyForm technology={editingTech} />}
        </DialogContent>
      </Dialog>
    </div>
  );
}